# AI智能体协作平台 - Cursor Rules

## 项目概述
这是一个基于Microsoft AutoGen v0.6和ReAct循环的企业级智能体协作平台，支持多智能体团队协作、实时流式输出、会话管理和可扩展架构。

## 技术栈
- **后端**: Python 3.8+, FastAPI, AutoGen v0.6, LangChain
- **AI框架**: Microsoft AutoGen SelectorGroupChat, ReAct循环
- **流式通信**: SSE (Server-Sent Events), WebSocket
- **模型**: OpenAI兼容API (阿里云通义千问 qwen-plus)
- **部署**: Docker, Kubernetes, Nginx, systemd

## 代码规范

### Python代码风格
- 遵循PEP 8代码风格规范
- 使用类型注解(Type Hints)
- 函数和类必须有文档字符串(docstring)
- 变量和函数使用snake_case命名
- 类名使用PascalCase命名
- 常量使用UPPER_CASE命名

### 文件结构规范
```
app/
├── agents/           # 智能体实现 - 继承BaseAutoGenAgent或BaseReactAgent
├── api/             # API接口层 - FastAPI路由和端点
├── core/            # 核心业务逻辑 - 智能体管理、SSE流式输出
├── config/          # 配置管理 - AutoGen配置、外部接口配置
├── tools/           # 工具集 - 可扩展的智能体工具
└── main.py          # 应用入口
```

### 智能体开发规范
1. **AutoGen智能体**: 继承`BaseAutoGenAgent`，实现`_initialize_autogen()`和`_run_autogen_workflow()`
2. **ReAct智能体**: 继承`BaseReactAgent`，实现`_register_tools()`
3. **必需属性**: `agent_id`, `name`, `description`, `icon`
4. **流式输出**: 必须使用AsyncGenerator返回结构化的SSE事件
5. **错误处理**: 实现完整的异常捕获和用户友好的错误信息

### SSE事件类型规范
- `session_info`: 会话信息
- `agent_start`: 智能体开始
- `thinking`: 智能体思考过程
- `decision`: 智能体决策信息
- `tool_start`: 工具开始执行
- `tool_streaming`: 工具流式输出
- `tool_result`: 工具执行结果
- `summary`: 最终总结
- `error`: 错误信息
- `stopped`: 任务停止

## AI助手行为规则

### 代码生成规则
1. **总是使用中文注释**，代码逻辑说明要详细
2. **AutoGen实现要点**:
   - 使用SelectorGroupChat进行多智能体协作
   - 实现selector_func函数控制智能体选择逻辑
   - 使用termination_condition控制工作流结束
   - 启用emit_team_events和model_client_streaming支持SSE
3. **SSE流式输出**:
   - 必须按轮次机制输出，清晰区分每轮对话
   - 每个事件必须包含timestamp和type字段
   - 错误处理要完整，避免流中断
4. **会话管理**:
   - 支持多用户会话隔离
   - 实现停止/恢复机制
   - 提供会话状态查询接口

### 调试和错误处理
1. **日志记录**: 使用logging模块，分级记录(DEBUG/INFO/WARNING/ERROR)
2. **异常处理**: 捕获所有可能的异常，返回用户友好的错误信息
3. **超时控制**: 所有异步操作都要设置合理的超时时间
4. **资源清理**: 确保会话和资源正确释放

### 性能优化
1. **并发处理**: 合理使用asyncio和异步编程
2. **内存管理**: 及时清理大对象，避免内存泄漏
3. **缓存策略**: 对重复计算结果进行缓存
4. **流式优化**: 减少延迟，提升用户体验

## 开发指导原则

### 新功能开发
1. **需求分析**: 明确功能需求和技术实现方案
2. **接口设计**: 定义清晰的API接口和数据结构
3. **单元测试**: 编写对应的测试用例
4. **文档更新**: 同步更新相关文档

### 代码审查重点
1. **功能正确性**: 验证功能是否按预期工作
2. **代码质量**: 检查代码风格、注释、类型注解
3. **性能影响**: 评估对系统性能的影响
4. **安全性**: 检查潜在的安全风险

### 特殊注意事项
1. **AutoGen版本**: 使用v0.6版本，注意与旧版本的差异
2. **模型调用**: 使用OpenAI兼容API，支持流式输出
3. **会话状态**: 正确管理会话生命周期，避免状态泄漏
4. **错误恢复**: 实现智能的错误恢复机制

## 提示词指导

### 代码生成提示
当需要生成智能体代码时，请包含：
- 完整的类定义和继承关系
- 详细的中文注释说明
- 完整的错误处理机制
- SSE事件的正确格式
- 符合项目架构的代码结构

### 调试提示
当遇到问题时，请检查：
- AutoGen配置是否正确
- SSE事件格式是否符合规范
- 会话管理是否正确实现
- 日志记录是否完整
- 异常处理是否充分

### 优化提示
代码优化重点关注：
- 异步编程的正确使用
- 内存和资源的有效管理
- 流式输出的性能优化
- 并发安全的考虑

## 常用代码模板

### AutoGen智能体模板
```python
class CustomAutoGenAgent(BaseAutoGenAgent):
    agent_id: str = "custom_agent"
    name: str = "自定义智能体"
    description: str = "智能体功能描述"
    icon: str = "🤖"

    def _initialize_autogen(self):
        # 模型客户端配置
        self.model_client = OpenAIChatCompletionClient(...)
        self._setup_team()

    def _setup_team(self):
        # 创建智能体团队
        # 定义selector_func和termination_condition
        # 创建SelectorGroupChat
        pass

    async def _run_autogen_workflow(self, query: str, context: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        # 实现工作流逻辑
        yield {"type": "status", "message": "开始处理"}
        # ... 具体实现
```

### SSE事件输出模板
```python
# 状态更新
yield {
    "type": "status",
    "message": "处理状态描述",
    "timestamp": datetime.now().isoformat()
}

# 智能体思考
yield {
    "type": "thinking", 
    "message": "智能体正在思考...",
    "agent_name": "智能体名称",
    "timestamp": datetime.now().isoformat()
}

# 决策输出
yield {
    "type": "decision",
    "content": "决策内容",
    "selected_speaker": "选中的智能体",
    "decision_details": {...},
    "timestamp": datetime.now().isoformat()
}
```

## 部署相关

### 环境变量
```bash
# 必需配置
OPENAI_API_KEY="your-api-key"
OPENAI_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1"
OPENAI_MODEL="qwen-plus"

# AutoGen配置
AUTOGEN_MAX_ROUND=10
AUTOGEN_TIMEOUT=300
AUTOGEN_STREAM_DELAY=0.1
```

### 依赖管理
- 核心依赖: autogen-agentchat>=0.4.0, autogen-core>=0.4.0
- Web框架: fastapi>=0.104.1, uvicorn>=0.24.0
- 流式支持: sse-starlette==1.6.5

### 监控和日志
- 日志文件: logs/app_YYYYMMDD.log
- 健康检查: /health端点
- 性能监控: 会话数量、响应时间、错误率

---

遵循以上规则，确保代码质量和项目一致性。在开发过程中，始终考虑用户体验、系统性能和代码可维护性。 