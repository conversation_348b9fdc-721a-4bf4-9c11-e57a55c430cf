# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.envrc

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# AI Agent Platform specific
# ==========================

# Log files
logs/
*.log

# Model cache
.cache/
cache/

# Temporary files
temp/
tmp/
*.tmp

# Session files
sessions/
*.session

# Configuration files with sensitive data
config/local_*
config/*_local*
config/*.secret
config/.env*

# Agent conversation history
conversations/
chat_history/

# Model outputs and artifacts
outputs/
artifacts/
generated/

# Database files
*.db
*.sqlite
*.sqlite3

# API keys and secrets
.env
.env.local
.env.production
.env.development
secrets.json
api_keys.txt

# Virtual environment variations
.venv*/
venv*/
env*/
.env*/

# Jupyter notebooks (if any)
*.ipynb

# Frontend build files (if any)
node_modules/
dist/
build/
.next/
.nuxt/

# Docker
.dockerignore
docker-compose.override.yml

# Backup files
*.bak
*.backup
*_backup

# Test artifacts
test_results/
test_outputs/
.pytest_cache/

# Performance profiling
*.prof
*.pstats

# Memory dumps
*.dump
*.dmp

# System files
.fuse_hidden*
.directory
.Trash-*

# Network files
.nfs*

# Package manager files
package-lock.json
yarn.lock

# Coverage reports
.coverage
coverage/
htmlcov/

# Benchmark results
benchmarks/

# AutoGen specific
# ================
# AutoGen agent cache
.autogen_cache/

# AutoGen conversation logs
autogen_conversations/

# Model fine-tuning outputs
fine_tuned_models/

# Agent state files
agent_states/

# Workflow artifacts
workflow_outputs/

# LangChain cache
.langchain_cache/

# OpenAI cache
.openai_cache/

# Custom model weights
models/
weights/
checkpoints/ 