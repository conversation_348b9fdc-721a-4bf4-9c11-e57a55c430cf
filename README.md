# 🤖 AI智能体协作平台

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![AutoGen](https://img.shields.io/badge/AutoGen-v0.4+-purple.svg)](https://github.com/microsoft/autogen)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个基于 **Microsoft AutoGen v0.4** 和 **ReAct循环** 的企业级智能体协作平台，支持多智能体团队协作、实时流式输出、会话管理和可扩展架构。

## 🌟 核心特性

### 🎯 多智能体协作
- **AutoGen SelectorGroupChat**: 基于Microsoft AutoGen v0.4的智能体选择机制
- **专业化分工**: 需求分析师、测试专家、开发工程师、评审专家等角色
- **智能路由**: 根据任务内容和对话历史自动选择下一个发言者
- **条件控制**: 支持复杂的业务流程控制和分支逻辑

### 🔄 ReAct循环引擎
- **思考-行动-观察**: 经典ReAct模式实现
- **流式输出**: 实时显示智能体思考、决策、工具调用过程
- **工具生态**: 可扩展的工具系统，支持文件解析、数据处理等
- **错误恢复**: 智能的错误处理和重试机制

### 🌊 实时流式交互
- **SSE (Server-Sent Events)**: 实时双向通信
- **轮次机制**: 清晰的对话轮次区分和状态管理
- **状态追踪**: 详细的执行状态和进度反馈
- **会话保持**: 支持长期会话状态维护

### 🏗️ 企业级架构
- **会话管理**: 多用户会话隔离和管理
- **停止/恢复**: 运行时任务控制和状态恢复
- **智能体切换**: 会话内智能体动态切换
- **日志系统**: 完整的日志记录和监控

## 🏛️ 系统架构

```
ai-agent-demo7/
├── app/                          # 主应用目录
│   ├── agents/                   # 智能体实现
│   │   ├── autogen_test_caseV7_agent.py           # 测试用例生成智能体V7
│   │   ├── autogen_requirements_review_agent.py   # 需求评审智能体
│   │   ├── autogen_requirements_analysisV2_agent.py # 需求分析智能体V2
│   │   ├── common/               # 智能体公共工具
│   │   │   ├── requirements_utils.py   # 需求处理工具
│   │   │   ├── streaming_utils.py      # 流式输出工具
│   │   │   └── test_case_organizer.py  # 测试用例编程式整理器
│   │   └── prompt/               # 提示词模板
│   │       ├── autogen_test_caseV7_prompt.py
│   │       ├── requirements_analysisV2_prompt.py
│   │       └── requirements_review_prompt.py
│   ├── api/                      # API接口层
│   │   ├── endpoints/
│   │   │   └── agent_endpoint.py # 智能体API端点
│   │   └── router.py             # 路由配置
│   ├── core/                     # 核心业务逻辑
│   │   ├── agent_manager.py      # 智能体管理器
│   │   ├── base_agent.py         # 智能体基类
│   │   ├── llm.py               # 大语言模型配置
│   │   ├── sse.py               # SSE流式输出
│   │   └── json_parser_pydantic.py # PydanticAI解析器
│   ├── config/                   # 配置管理
│   │   ├── autogen_config.py     # AutoGen配置
│   │   └── external_interface_url_config.py # 外部接口配置
│   ├── tools/                    # 工具集
│   │   └── autogen_log_control.py # AutoGen日志控制工具
│   └── main.py                   # 应用入口
├── front/                        # 前端界面
│   ├── src/
│   │   ├── components/          # Vue组件
│   │   ├── views/              # 页面视图
│   │   └── api/                # API接口
│   └── package.json
├── logs/                         # 日志文件
├── requirements.txt              # 依赖管理
└── README.md                     # 项目文档
```

## 🚀 快速开始

### 1. 环境准备

**系统要求**:
- Python 3.8+
- macOS/Linux/Windows

**克隆项目**:
```bash
git clone <repository-url>
cd ai-agent-demo7
```

### 2. 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# macOS/Linux:
source venv/bin/activate

# Windows:
venv\Scripts\activate
```

### 3. 安装依赖

**确保虚拟环境已激活后再安装**:

```bash
# 确认虚拟环境已激活 (命令行前应显示 (venv))
# 如果没有激活，先激活虚拟环境：
# macOS/Linux: source venv/bin/activate
# Windows: venv\Scripts\activate

# 使用阿里云源加速安装（推荐）
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 或使用默认源
pip install -r requirements.txt
```

**核心依赖包**:
- `fastapi>=0.104.1` - Web框架
- `autogen-agentchat>=0.4.0` - AutoGen智能体聊天
- `autogen-core>=0.4.0` - AutoGen核心
- `autogen-ext[openai]>=0.4.0` - AutoGen扩展
- `langchain>=0.0.335` - ReAct循环支持
- `sse-starlette==1.6.5` - SSE支持
- `pydantic>=2.4.0` - 数据验证和解析

### 4. 环境变量配置

创建 `.env` 文件或设置环境变量：

```bash
# OpenAI兼容API配置 (使用阿里云通义千问)
export OPENAI_API_KEY=""
export OPENAI_API_BASE="" 
export OPENAI_MODEL=""

# AutoGen配置
export AUTOGEN_MODEL="qwen-plus"
export AUTOGEN_MAX_ROUND="10"
export AUTOGEN_TIMEOUT="300"
export AUTOGEN_STREAM_DELAY="0.1"

# AutoGen日志控制
export AUTOGEN_LOG_LEVEL="ERROR"
export AUTOGEN_SUPPRESS_LOGS="True"
export AUTOGEN_SHOW_ERRORS_ONLY="True"
```

### 5. 启动应用

```bash
# 开发模式 (支持热重载)
python -m app.main

# 或使用uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

服务将在 **http://localhost:8000** 启动

## 📚 API文档

### 智能体API

#### 获取智能体列表
```bash
GET /api/agent/list
```

**响应示例**:
```json
{
  "agents": [
    {
      "id": "autogen_test_case_v7_agent",
      "name": "测试用例编写V7",
      "description": "基于AutoGen专业测试用例生成智能体，支持多智能体协作和流式输出",
      "icon": "🧪",
      "type": "autogen"
    },
    {
      "id": "requirements_analysis_v2_agent", 
      "name": "需求分析V2",
      "description": "基于AutoGen的专业需求分析智能体，支持深度需求分析、风险识别、测试策略制定",
      "icon": "📋",
      "type": "autogen"
    }
  ]
}
```

#### 运行智能体 (SSE流式)
```bash
POST /api/agent/run
Content-Type: application/json

{
  "agent_id": "autogen_test_case_v7_agent",
  "query": "编写用户登录功能的测试用例",
  "session_id": "optional-session-id",
  "user_id": "optional-user-id",
  "context": {}
}
```

**SSE事件类型**:
- `session_info`: 会话信息
- `agent_start`: 智能体开始
- `thinking`: 智能体思考
- `decision`: 智能体决策
- `tool_start`: 工具开始执行
- `tool_streaming`: 工具流式输出
- `tool_result`: 工具执行结果
- `summary`: 最终总结
- `error`: 错误信息
- `stopped`: 任务停止

#### 文件上传和解析
```bash
POST /api/agent/upload-file
Content-Type: multipart/form-data

Form Data:
- file: 上传的文件 (支持 .txt, .md, .json, .csv, .docx, .pdf, .xlsx)
```

**响应示例**:
```json
{
  "success": true,
  "content": "解析后的文件内容",
  "filename": "test.txt",
  "file_type": "text/plain",
  "message": "文件解析成功"
}
```

#### 会话管理

**停止会话**:
```bash
POST /api/agent/stop
{
  "session_id": "session-id",
  "reason": "用户主动停止"
}
```

**恢复会话**:
```bash
POST /api/agent/resume
{
  "session_id": "session-id"
}
```

**切换智能体**:
```bash
POST /api/agent/switch
{
  "session_id": "session-id",
  "new_agent_id": "new-agent-id"
}
```

**获取会话列表**:
```bash
GET /api/agent/sessions?user_id=optional-user-id
```

## 🤖 智能体详解

### 1. 测试用例生成智能体V7 (`autogen_test_case_v7_agent`)

**功能**: 多智能体协作生成专业测试用例
**协作流程**: 需求分析师 → 测试用例设计师 → 测试用例审核员
**特色功能**:
- 编程式测试用例整理器，智能去重和组织
- 支持输入需求文档获取需求进行用例编写
- PydanticAI增强的JSON解析能力
- 多维度相似度检测和模块合并

**使用示例**:
```bash
curl -X POST "http://localhost:8000/api/agent/run" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_id": "autogen_test_case_v7_agent",
    "query": "用户登录功能需求：支持手机号、邮箱登录，包含验证码验证，支持记住密码功能"
  }'
```

### 2. 需求分析智能体V2 (`requirements_analysis_v2_agent`)

**功能**: 深度需求分析和风险识别
**协作流程**: 需求分析师 → 测试专家 → 评审专家 → 总结专家
**特色功能**:
- 功能点拆分和逻辑流程分析
- 测试场景识别和风险评估
- 歧义检测和矛盾点检测
- 结构化总结报告生成

### 3. 需求评审智能体 (`requirements_review_agent`)

**功能**: 模拟互联网产品需求评审会议
**协作角色**: 产品经理、开发工程师、测试工程师、UI/UX设计师、争议分析师、总结分析师
**特色功能**:
- 多角度专业评审
- 争议点识别和处理
- 综合评审报告生成
- 多轮讨论支持

## 🛠️ 开发指南

### 创建自定义AutoGen智能体

1. **继承基类**:
```python
from app.core.base_agent import BaseAutoGenAgent

class CustomAutoGenAgent(BaseAutoGenAgent):
    agent_id: str = "custom_agent"
    name: str = "自定义智能体"
    description: str = "智能体描述"
    icon: str = "🔧"
```

2. **实现初始化方法**:
```python
def _initialize_autogen(self):
    # 配置模型客户端
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    self.model_client = OpenAIChatCompletionClient(
        model=os.getenv("OPENAI_MODEL"),
        api_key=os.getenv("OPENAI_API_KEY"),
        base_url=os.getenv("OPENAI_API_BASE")
    )
    self._setup_team()
```

3. **设置智能体团队**:
```python
def _setup_team(self):
    from autogen_agentchat.agents import AssistantAgent
    from autogen_agentchat.teams import SelectorGroupChat
    
    # 创建智能体
    self.agent1 = AssistantAgent(
        name="Agent1",
        model_client=self.model_client,
        system_message="你的系统提示",
        model_client_stream=True
    )
    
    # 定义选择器函数
    def selector_func(messages):
        # 智能体选择逻辑
        return "Agent1"
    
    # 创建SelectorGroupChat
    self.team = SelectorGroupChat(
        participants=[self.agent1],
        model_client=self.model_client,
        selector_func=selector_func,
        termination_condition=lambda messages: len(messages) > 10,
        emit_team_events=True,
        model_client_streaming=True
    )
```

4. **实现工作流**:
```python
async def _run_autogen_workflow(self, query: str, context: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
    # 工作流实现
    yield {"type": "agent_start", "message": "智能体开始工作"}
    
    # 运行团队协作
    async for event in self.team.run_stream(query):
        # 处理事件并输出SSE格式
        yield self._process_autogen_event(event)
```

### 创建ReAct循环智能体

1. **继承ReAct基类**:
```python
from app.core.base_agent import BaseReactAgent

class CustomReactAgent(BaseReactAgent):
    agent_id: str = "custom_react_agent"
    name: str = "自定义ReAct智能体"
```

2. **注册工具**:
```python
def _register_tools(self):
    self.add_tool(
        name="calculator",
        function_name="计算器",
        description="进行数学计算",
        func=self._calculator_func,
        parameters=["expression"]
    )
```

### 使用PydanticAI增强解析

1. **定义数据模型**:
```python
from pydantic import BaseModel, Field

class TestCase(BaseModel):
    name: str = Field(..., description="用例名称")
    steps: List[str] = Field(..., description="测试步骤")
    expected: str = Field(..., description="预期结果")
```

2. **使用解析器**:
```python
from app.core.json_parser_pydantic import PydanticJSONParser

parser = PydanticJSONParser()
parsed_data = parser.parse_test_cases(json_text, TestCase)
```

## 📊 监控和日志

### AutoGen日志控制

项目提供了完善的AutoGen日志控制工具：

#### 环境变量控制
```bash
# 在 .env 文件中配置
AUTOGEN_LOG_LEVEL=ERROR           # 只显示错误
AUTOGEN_SUPPRESS_LOGS=True        # 完全抑制详细日志
AUTOGEN_SHOW_ERRORS_ONLY=True     # 只显示错误级别日志
```

#### 命令行工具控制
```bash
# 完全抑制AutoGen日志
python app/tools/autogen_log_control.py suppress

# 查看当前状态
python app/tools/autogen_log_control.py status

# 设置特定级别
python app/tools/autogen_log_control.py set ERROR

# 交互式控制
python app/tools/autogen_log_control.py
```

#### 代码中控制
```python
from app.tools.autogen_log_control import suppress_autogen_logs

# 在需要的地方调用
suppress_autogen_logs()
```

### 日志系统

- **控制台日志**: INFO级别，实时输出
- **文件日志**: DEBUG级别，按日期分文件存储
- **日志文件位置**: `logs/app_YYYYMMDD.log`

### 日志格式
```
2024-06-23 14:30:25 | INFO     | app.core.agent_manager | 🚀 开始运行智能体: autogen_test_case_v7_agent
```

## 🚢 部署指南

### Docker部署

1. **创建Dockerfile**:
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

COPY . .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

2. **构建和运行**:
```bash
docker build -t ai-agent-platform .
docker run -p 8000:8000 -e OPENAI_API_KEY=your-key ai-agent-platform
```

### 云服务器部署

1. **安装依赖**:
```bash
# 系统依赖
sudo apt update
sudo apt install python3 python3-pip python3-venv nginx

# 项目依赖
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

2. **配置systemd服务**:
```ini
# /etc/systemd/system/ai-agent.service
[Unit]
Description=AI Agent Platform
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/ai-agent-demo7
Environment=PATH=/path/to/ai-agent-demo7/venv/bin
ExecStart=/path/to/ai-agent-demo7/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always

[Install]
WantedBy=multi-user.target
```

3. **启动服务**:
```bash
sudo systemctl enable ai-agent.service
sudo systemctl start ai-agent.service
```

## 🔧 配置说明

### AutoGen配置 (`app/config/autogen_config.py`)

```python
# OpenAI配置
openai_api_key: str = "your-api-key"
openai_model: str = ""  
openai_base_url: str = ""

# 智能体配置
max_round: int = 10              # 最大对话轮次
timeout: int = 300               # 超时时间(秒)
stream_delay: float = 0.1        # 流式输出延迟

# 日志控制配置
autogen_log_level: str = "WARNING"
suppress_autogen_logs: bool = True
show_autogen_errors_only: bool = True
```

## 🧪 测试

### 运行测试

```bash
# 单元测试
python -m pytest tests/

# 集成测试
python -m pytest tests/integration/

# 覆盖率测试
python -m pytest --cov=app tests/
```

### API测试示例

```bash
# 测试智能体列表
curl -X GET "http://localhost:8000/api/agent/list"

# 测试运行智能体
curl -X POST "http://localhost:8000/api/agent/run" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_id": "autogen_test_case_v7_agent",
    "query": "编写登录功能测试用例"
  }'

# 测试文件上传
curl -X POST "http://localhost:8000/api/agent/upload-file" \
  -F "file=@test.txt"
```

## 🆘 故障排除

### 常见问题

**Q: AutoGen初始化失败**
```
A: 检查OpenAI API配置，确保API密钥有效且网络连接正常
```

**Q: SSE连接中断**
```
A: 检查代理服务器配置，确保支持长连接和流式传输
```

**Q: AutoGen日志太多**
```
A: 使用环境变量 AUTOGEN_SUPPRESS_LOGS=True 或运行 python app/tools/autogen_log_control.py suppress
```

**Q: 智能体选择失败**
```
A: 检查selector_func逻辑，确保返回有效的智能体名称
```

### 性能优化

1. **调整并发数**: 根据服务器性能调整智能体并发执行数
2. **优化模型调用**: 使用流式输出减少延迟
3. **缓存策略**: 对重复请求进行结果缓存
4. **资源监控**: 监控内存和CPU使用率

## 🎯 功能特性

- ✅ 多智能体协作 (AutoGen SelectorGroupChat)
- ✅ 实时流式输出 (SSE)
- ✅ 会话管理和状态控制
- ✅ 可扩展的智能体架构
- ✅ 完善的日志控制机制
- ✅ ReAct循环智能体支持
- ✅ PydanticAI增强解析
- ✅ 文件上传和解析功能
- ✅ 编程式测试用例整理
- ✅ 企业级部署支持

## 🤝 贡献指南

1. **Fork项目**
2. **创建特性分支**: `git checkout -b feature/amazing-feature`
3. **提交更改**: `git commit -m 'Add amazing feature'`
4. **推送分支**: `git push origin feature/amazing-feature`
5. **提交Pull Request**

### 代码规范

- 使用PEP 8代码风格
- 添加类型注解
- 编写文档字符串
- 添加单元测试

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源协议。

## 📞 支持

- **Issues**: [GitHub Issues](issues-url)
- **文档**: [项目文档](docs-url)
- **邮箱**: <EMAIL>

---

🚀 **开始你的AI智能体协作之旅吧！** 



