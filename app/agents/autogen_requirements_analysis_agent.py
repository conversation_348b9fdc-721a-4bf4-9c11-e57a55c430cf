import json
import os
import sys
from typing import List, Dict, Any, AsyncGenerator, Optional
import asyncio
import time
import logging
from datetime import datetime

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import SelectorGroupChat
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.messages import TextMessage, ModelClientStreamingChunkEvent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo, ModelFamily
from autogen_agentchat.conditions import FunctionalTermination, MaxMessageTermination
from autogen_agentchat.base import Response
from autogen_core.model_context import BufferedChatCompletionContext

# 项目内部导入
from app.core.base_agent import BaseAutoGenAgent
from app.config.autogen_config import autogen_config, TERMINATION_KEYWORDS
from app.core.sse import format_sse_data
from app.agents.common.requirements_utils import get_requirements_from_url
from app.agents.prompt.requirements_analysis_prompt import RequirementsAnalysisV2Prompts

# 配置日志
logger = logging.getLogger(__name__)

class RequirementsAnalysisV2Agent(BaseAutoGenAgent):
    """
    基于AutoGen的需求分析智能体V2，专注于深度需求分析和风险识别
    使用SelectorGroupChat实现多智能体团队协作：需求分析师 → 测试专家 → 评审专家 → 总结专家
    支持流式输出和条件控制机制，最终结果通过总结专家以SSE summary类型输出
    继承BaseAutoGenAgent以集成项目架构
    """
    
    # 智能体元信息
    agent_id = "requirements_analysis_agent"
    name = "需求分析V2"
    description = "基于AutoGen的专业需求分析智能体，支持深度需求分析、风险识别、测试策略制定、质量评审和智能总结，最终通过总结专家输出结构化报告"
    icon = "📋"
    
    def __init__(self):
        """初始化需求分析智能体"""
        super().__init__()
        self.analysis_results = {"需求分析结果": {}}
        self.retry_count = 0
        self.max_retries = 2
        self._current_decision = None  # 存储当前决策信息
        self._final_summary = None  # 存储最终汇总信息
        
        # 初始化AutoGen组件
        self._initialize_autogen()
    
    def _initialize_autogen(self):
        """初始化AutoGen组件"""
        try:
            # 从配置读取模型参数
            self.model_client = OpenAIChatCompletionClient(
                model=os.getenv("OPENAI_MODEL"),
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_API_BASE"),
                model_info=ModelInfo(
                    vision=True,
                    function_calling=True,
                    json_output=True,
                    family="qwen",
                    structured_output=False
                )
            )
            
            # 设置智能体团队
            self.analysis_results = {"需求分析结果": {}}
            self._setup_team()
            
        except Exception as e:
            logger.error(f"AutoGen组件初始化失败: {e}")
            raise
    
    def _setup_team(self):
        """设置智能体团队 - 需求分析师 → 测试专家 → 评审专家"""
        
        # 需求分析师
        self.requirements_analyst = AssistantAgent(
            name="Requirements_Analyst",
            model_client=self.model_client,
            model_client_stream=True,  # 启用流式输出
            description="资深需求分析师，专注于深度需求分析和风险识别",
            system_message=RequirementsAnalysisV2Prompts._prompt_requirements_analyst()
        )
        
        # 测试专家
        self.test_expert = AssistantAgent(
            name="Test_Expert",
            model_client=self.model_client,
            model_client_stream=True,  # 启用流式输出
            description="资深测试专家，专注于测试策略制定和质量保证",
            system_message=RequirementsAnalysisV2Prompts._prompt_test_expert()
        )
        
        # 评审专家
        self.evaluation_expert = AssistantAgent(
            name="Evaluation_Expert",
            model_client=self.model_client,
            model_client_stream=True,  # 启用流式输出
            description="项目评审专家，负责综合评审和质量评估",
            system_message=RequirementsAnalysisV2Prompts._prompt_evaluation_expert()
        )
        
        # 总结专家
        self.summary_expert = AssistantAgent(
            name="Summary_Expert",
            model_client=self.model_client,
            model_client_stream=True,  # 启用流式输出
            description="专业总结专家，负责整合所有分析结果并生成最终总结报告",
            system_message=RequirementsAnalysisV2Prompts._prompt_summary_expert()
        )
        
        # 创建团队成员列表
        self.participants = [
            self.requirements_analyst,
            self.test_expert,
            self.evaluation_expert,
            self.summary_expert
        ]
        
        # 定义智能体选择函数
        def selector_func(messages):
            """根据对话历史和条件逻辑选择下一个发言者"""
            if not messages:
                decision_info = {
                    "reason": "需求分析工作流开始",
                    "selected_agent": "Requirements_Analyst",
                    "current_stage": "深度需求分析阶段",
                    "decision_logic": "工作流初始化，首先由需求分析师进行深度需求分析"
                }
                self._current_decision = decision_info
                return "Requirements_Analyst"
            
            last_message = messages[-1]
            last_speaker = getattr(last_message, 'source', '')
            last_content = getattr(last_message, 'content', '')
            
            decision_info = {
                "last_speaker": last_speaker,
                "message_content_preview": last_content[:100] + "..." if len(last_content) > 100 else last_content,
                "current_stage": "",
                "decision_logic": "",
                "reason": "",
                "selected_agent": None,
                "retry_count": self.retry_count
            }
            
            # 条件分支逻辑
            if last_speaker == "Requirements_Analyst":
                if "需求深度分析完成" in last_content:
                    decision_info.update({
                        "reason": "需求分析完成",
                        "selected_agent": "Test_Expert",
                        "current_stage": "测试策略制定阶段",
                        "decision_logic": "检测到需求分析师说'需求深度分析完成'，工作流进入测试策略制定阶段"
                    })
                    self._current_decision = decision_info
                    return "Test_Expert"
                else:
                    decision_info.update({
                        "reason": "需求分析未完成",
                        "selected_agent": "Requirements_Analyst",
                        "current_stage": "深度需求分析阶段",
                        "decision_logic": "需求分析师尚未完成深度分析，继续由需求分析师发言"
                    })
                    self._current_decision = decision_info
                    return "Requirements_Analyst"
                    
            elif last_speaker == "Test_Expert":
                if "测试策略分析完成" in last_content:
                    decision_info.update({
                        "reason": "测试策略分析完成",
                        "selected_agent": "Evaluation_Expert",
                        "current_stage": "综合评审阶段",
                        "decision_logic": "检测到测试专家说'测试策略分析完成'，工作流进入综合评审阶段"
                    })
                    self._current_decision = decision_info
                    return "Evaluation_Expert"
                else:
                    decision_info.update({
                        "reason": "测试策略分析未完成",
                        "selected_agent": "Test_Expert",
                        "current_stage": "测试策略制定阶段",
                        "decision_logic": "测试专家尚未完成策略分析，继续由测试专家发言"
                    })
                    self._current_decision = decision_info
                    return "Test_Expert"
                 
            elif last_speaker == "Evaluation_Expert":
                if "需要重新分析" in last_content:
                    self.retry_count += 1
                    if self.retry_count < self.max_retries:
                        decision_info.update({
                            "reason": "评审不通过，需要重新分析",
                            "selected_agent": "Requirements_Analyst",
                            "current_stage": "需求重新分析阶段",
                            "decision_logic": f"评审专家要求重新分析，当前重试次数: {self.retry_count}/{self.max_retries}"
                        })
                        self._current_decision = decision_info
                        return "Requirements_Analyst"
                    else:
                        decision_info.update({
                            "reason": "达到最大重试次数",
                            "selected_agent": None,
                            "current_stage": "工作流结束",
                            "decision_logic": f"已达到最大重试次数({self.max_retries})，工作流终止"
                        })
                        self._current_decision = decision_info
                        return None
                elif "需求评审汇总完成" in last_content:
                    decision_info.update({
                        "reason": "评审完成，进入总结阶段",
                        "selected_agent": "Summary_Expert",
                        "current_stage": "最终总结阶段",
                        "decision_logic": "评审专家完成评审，现在由总结专家生成最终总结报告"
                    })
                    self._current_decision = decision_info
                    return "Summary_Expert"
                else:
                    decision_info.update({
                        "reason": "评审状态未明确",
                        "selected_agent": None,
                        "current_stage": "工作流结束",
                        "decision_logic": "评审专家未明确表态，工作流结束"
                    })
                    self._current_decision = decision_info
                    return None
                    
            elif last_speaker == "Summary_Expert":
                if "需求分析总结完成" in last_content:
                    decision_info.update({
                        "reason": "总结专家完成最终总结",
                        "selected_agent": None,
                        "current_stage": "工作流完成",
                        "decision_logic": "总结专家完成最终总结报告，整个工作流正常结束"
                    })
                    self._current_decision = decision_info
                    return None
                else:
                    decision_info.update({
                        "reason": "总结进行中",
                        "selected_agent": "Summary_Expert",
                        "current_stage": "最终总结阶段",
                        "decision_logic": "总结专家继续完成总结工作"
                    })
                    self._current_decision = decision_info
                    return "Summary_Expert"
            
            # 默认情况
            decision_info.update({
                "reason": "未知状态",
                "selected_agent": None,
                "current_stage": "工作流结束",
                "decision_logic": "遇到未预期的情况，工作流结束"
            })
            self._current_decision = decision_info
            return None
        
        # 创建自定义终止条件
        def custom_termination_condition(messages):
            """自定义终止条件：只有总结专家说出包含"需求分析总结完成"的消息时才终止"""
            if not messages:
                return False
            
            last_message = messages[-1]
            last_speaker = getattr(last_message, 'source', '')
            last_content = getattr(last_message, 'content', '')
            
            if last_speaker == "Summary_Expert":
                if "需求分析总结完成" in last_content:
                    return True
                    
            return False
        
        # 组合终止条件
        termination_condition = (
            FunctionalTermination(custom_termination_condition) |
            MaxMessageTermination(20)
        )
        
        # 创建SelectorGroupChat团队
        self.team = SelectorGroupChat(
            participants=self.participants,
            model_client=self.model_client,
            termination_condition=termination_condition,
            max_turns=10,
            selector_func=selector_func,
            allow_repeated_speaker=True,
            max_selector_attempts=3,
            emit_team_events=True,
            model_client_streaming=True
        )
    
    async def _run_autogen_workflow(self, query: str, context: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """
        运行AutoGen需求分析工作流的核心实现 - 按照轮次机制输出SSE
        工作流：需求分析师深度分析 → 测试专家制定策略 → 评审专家综合评审 → 总结专家生成最终报告
        
        Args:
            query: 用户查询（需求文档内容或URL）
            context: 上下文信息
            
        Yields:
            流式输出的结果数据
        """
        # 重置所有状态 - 确保会话隔离
        self._reset_agent_state()
        
        start_time = time.time()
        
        try:
            # 1. 工作流开始
            yield {
                "type": "agent_start",
                "message": "需求分析专家智能体启动",
                "agent_id": self.agent_id,
                "agent_name": self.name,
                "timestamp": datetime.now().isoformat()
            }
            
            # 2. 使用工具获取需求文档内容
            yield {
                "type": "thinking", 
                "message": "正在获取需求文档内容...",
                "timestamp": datetime.now().isoformat()
            }
            
            # 直接调用工具获取需求文档
            processed_requirements = await get_requirements_from_url(query)
            
            yield {
                "type": "tool_result",
                "tool_name": "requirements_tool",
                "result": f"需求文档获取完成，开始多智能体协作进行需求分析，需求内容：" + processed_requirements[:100] + "...",
                "agent_name": "requirements_tool",
                "timestamp": datetime.now().isoformat()
            }
            
            # 构造团队协作任务
            task = f"""
我们是一个专业的需求分析团队，现在需要对以下需求文档进行全面的深度分析：

用户输入内容：
{query}

需求文档内容：
{processed_requirements}

请按照SelectorGroupChat智能工作流协作：
1. 需求分析师：进行深度需求分析，包括功能点拆分、逻辑流程分析、测试场景识别、风险评估、歧义检测、矛盾点检测等
2. 测试专家：基于需求分析结果，制定测试策略和测试计划，评估质量保证措施
3. 评审专家：对需求分析和测试策略进行综合评审，给出需求质量评估，完成后说"需求评审汇总完成"
4. 总结专家：整合所有专家的分析结果，生成最终的结构化总结报告，完成后说"需求分析总结完成"

团队将按照条件控制顺序协作，总结专家完成最终总结后整个流程结束。

现在开始智能协作！
"""
            
            # 运行团队协作流式处理 - 按轮次处理
            turn_number = 0
            current_speaker = None
            current_turn_messages = []
            streaming_buffer = ""
            is_in_agent_execution = False
            
            async for message in self.team.run_stream(task=task):
                # 检查是否被取消
                if self.is_cancelled:
                    yield {
                        "type": "stopped",
                        "message": f"任务已被取消: {self.cancel_reason}",
                        "timestamp": datetime.now().isoformat()
                    }
                    break
                
                # 1. 处理SelectSpeakerEvent - 轮次开始，选择发言者
                if hasattr(message, 'type') and message.type == 'SelectSpeakerEvent':
                    # 结束上一个智能体的执行（如果有）
                    if is_in_agent_execution and current_speaker:
                        yield {
                            "type": "tool_result",
                            "tool_name": f"{current_speaker}_turn_{turn_number}",
                            "result": f"{current_speaker}完成当前阶段工作",
                            "agent_name": current_speaker,
                            "accumulated_content": streaming_buffer,
                            "timestamp": datetime.now().isoformat()
                        }
                        is_in_agent_execution = False
                    
                    # 开始新轮次
                    turn_number += 1
                    selected_speakers = message.content
                    current_speaker = selected_speakers[0] if selected_speakers else None
                    current_turn_messages = []
                    streaming_buffer = ""
                    
                    if current_speaker and current_speaker != "user":
                        # 思考阶段
                        yield {
                            "type": "thinking",
                            "message": f"智能体选择：{current_speaker}, 第{turn_number}轮，开始执行...",
                            "turn_number": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        # 决策阶段
                        decision_info = getattr(self, '_current_decision', None) or {
                            "reason": f"选择{current_speaker}执行",
                            "current_stage": f"{current_speaker}阶段",
                            "decision_logic": f"根据工作流逻辑选择{current_speaker}执行第{turn_number}轮"
                        }
                        
                        # 工具执行开始
                        yield {
                            "type": "tool_start",
                            "tool_name": f"{current_speaker}_turn_{turn_number}",
                            "function_name": f"{current_speaker}专业分析",
                            "parameters": {
                                "智能体": current_speaker, 
                                "轮次": turn_number,
                                "阶段": decision_info.get("current_stage", "分析阶段")
                            },
                            "turn_number": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        is_in_agent_execution = True
                
                # 2. 处理ModelClientStreamingChunkEvent - 智能体思考流式输出
                elif isinstance(message, ModelClientStreamingChunkEvent):
                    if current_speaker and current_speaker != "user" and is_in_agent_execution:
                        streaming_buffer += message.content
                        
                        # 流式输出智能体的思考过程
                        yield {
                            "type": "tool_streaming",
                            "tool_name": f"{current_speaker}_turn_{turn_number}",
                            "content": message.content,
                            "accumulated": streaming_buffer,
                            "agent_name": current_speaker,
                            "turn_number": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                
                # 3. 处理完整消息 - 智能体完成一轮发言
                elif hasattr(message, 'content') and hasattr(message, 'source'):
                    current_turn_messages.append(message)
                    
                    if message.source != "user" and message.source == current_speaker and is_in_agent_execution:
                        # 智能体完成当前轮次的发言
                        content_str = str(message.content) if not isinstance(message.content, str) else message.content
                        
                        # 检查是否是阶段完成的标志
                        completion_keywords = ["需求深度分析完成", "测试策略分析完成", "需求评审汇总完成", "需求分析总结完成"]
                        if any(keyword in content_str for keyword in completion_keywords):
                            # 阶段完成
                            yield {
                                "type": "tool_result",
                                "tool_name": f"{current_speaker}_turn_{turn_number}",
                                "result": f"{current_speaker}成功完成专业分析",
                                "agent_name": current_speaker,
                                "message_content": content_str[:200] + "..." if len(content_str) > 200 else content_str,
                                "accumulated_content": streaming_buffer,
                                "turn_number": turn_number,
                                "timestamp": datetime.now().isoformat()
                            }
                            
                            is_in_agent_execution = False
                        else:
                            # 智能体还在继续当前轮次的工作
                            yield {
                                "type": "tool_streaming",
                                "tool_name": f"{current_speaker}_turn_{turn_number}",
                                "content": f"\n[{current_speaker}继续分析中...]",
                                "accumulated": streaming_buffer + f"\n[继续分析...] ",
                                "agent_name": current_speaker,
                                "turn_number": turn_number,
                                "timestamp": datetime.now().isoformat()
                            }
                
                # 4. 处理TaskResult - 整个工作流完成
                elif hasattr(message, 'messages'):
                    # 结束最后一个智能体的执行
                    if is_in_agent_execution and current_speaker:
                        # 如果是总结专家完成，则通过summary类型输出其总结内容
                        if current_speaker == "Summary_Expert":
                            yield {
                                "type": "summary",
                                "message": "总结专家完成最终需求分析总结",
                                "content": streaming_buffer,
                                "agent_name": current_speaker,
                                "turn_number": turn_number,
                                "timestamp": datetime.now().isoformat()
                            }
                        else:
                            yield {
                                "type": "tool_result",
                                "tool_name": f"{current_speaker}_turn_{turn_number}",
                                "result": f"{current_speaker}完成最终分析",
                                "agent_name": current_speaker,
                                "accumulated_content": streaming_buffer,
                                "turn_number": turn_number,
                                "timestamp": datetime.now().isoformat()
                            }
                        is_in_agent_execution = False
                    
                    # 工作流完成，提取总结专家的内容作为最终结果
                    summary_content = ""
                    for msg in message.messages:
                        if (hasattr(msg, 'source') and msg.source == "Summary_Expert" and 
                            hasattr(msg, 'content') and msg.content):
                            summary_content = str(msg.content)
                            break
                    
                    # 如果没有找到总结专家的内容，则从streaming_buffer获取
                    if not summary_content and current_speaker == "Summary_Expert":
                        summary_content = streaming_buffer
                    
                    # 如果还是没有内容，则进行基本的结果提取
                    if not summary_content:
                        yield {
                            "type": "thinking",
                            "message": f"所有智能体协作完成（共{turn_number}轮），正在整理需求分析结果...",
                            "total_turns": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        # 提取分析结果
                        await self._extract_analysis_results_from_messages(message.messages)
                        
                        # 生成基本的分析统计信息
                        analysis_stats = self._get_analysis_statistics()
                        end_time = time.time()
                        duration = end_time - start_time
                        
                        # 生成简单的总结
                        basic_summary = f"""# 📋 需求分析完成

**分析时长：** {duration:.2f}秒  
**协作轮次：** {turn_number}轮  
**分析模块：** {analysis_stats.get('analysis_sections', 0)}个  

所有智能体协作分析已完成，具体分析结果已保存到系统中。"""
                        
                        yield {
                            "type": "summary",
                            "message": "需求分析任务完成",
                            "content": basic_summary,
                            "statistics": analysis_stats,
                            "duration": f"{duration:.2f}秒",
                            "total_turns": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                    else:
                        # 输出总结专家的完整总结内容
                        yield {
                            "type": "summary",
                            "message": "需求分析总结专家完成最终总结报告",
                            "content": summary_content,
                            "agent_name": "Summary_Expert",
                            "total_turns": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                    
                    break
    
            # 工作流完成，输出最终状态
            yield {
                "type": "status",
                "message": f"需求分析已成功完成（共{turn_number}轮协作）",
                "timestamp": datetime.now().isoformat()
            }
            
            yield {
                "type": "complete",
                "message": "AutoGen需求分析任务完成",
                "success": True,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"AutoGen工作流执行错误: {e}", exc_info=True)
            yield {
                "type": "error",
                "message": f"分析过程中出现错误: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
            raise
    
    async def _extract_analysis_results_from_messages(self, messages, progress_callback=None):
        """
        从对话消息中提取需求分析结果
        
        Args:
            messages: 对话消息列表
            progress_callback: 进度回调函数，用于流式输出进度
        """
        logger.info("🔍 正在从对话中提取需求分析结果...")
        
        if progress_callback:
            await progress_callback({
                "type": "tool_streaming",
                "tool_name": "extract_analysis_results",
                "content": f"开始分析{len(messages)}条对话消息...",
                "accumulated": f"开始分析{len(messages)}条对话消息...",
                "timestamp": datetime.now().isoformat()
            })
        
        processed_count = 0
        analysis_sections = {
            "Requirements_Analyst": [],
            "Test_Expert": [],
            "Evaluation_Expert": [],
            "Summary_Expert": []
        }
        
        # 遍历消息，按智能体分类收集分析内容
        for i, message in enumerate(messages):
            if hasattr(message, 'content') and hasattr(message, 'source'):
                content = message.content
                source = message.source
                processed_count += 1
                
                # 安全处理content
                if not isinstance(content, str):
                    content = str(content)
                
                # 根据智能体分类收集内容
                if source in analysis_sections:
                    analysis_sections[source].append(content)
                    
                    if progress_callback and len(content) > 100:
                        await progress_callback({
                            "type": "tool_streaming",
                            "tool_name": "extract_analysis_results",
                            "content": f"收集{source}的分析内容...",
                            "accumulated": f"已处理{processed_count}/{len(messages)}条消息",
                            "timestamp": datetime.now().isoformat()
                        })
                
                # 定期报告进度
                elif progress_callback and (i + 1) % 5 == 0:
                    await progress_callback({
                        "type": "tool_streaming",
                        "tool_name": "extract_analysis_results",
                        "content": f"继续分析中... ({i + 1}/{len(messages)})",
                        "accumulated": f"已处理{processed_count}/{len(messages)}条消息",
                        "timestamp": datetime.now().isoformat()
                    })
        
        # 整理分析结果
        self.analysis_results = {
            "需求分析结果": {
                "需求深度分析": {
                    "分析师": "Requirements_Analyst",
                    "分析内容": analysis_sections["Requirements_Analyst"],
                    "分析时间": datetime.now().isoformat()
                },
                "测试策略分析": {
                    "专家": "Test_Expert", 
                    "策略内容": analysis_sections["Test_Expert"],
                    "分析时间": datetime.now().isoformat()
                },
                "综合评审报告": {
                    "评审专家": "Evaluation_Expert",
                    "评审内容": analysis_sections["Evaluation_Expert"],
                    "评审时间": datetime.now().isoformat()
                },
                "最终总结报告": {
                    "总结专家": "Summary_Expert",
                    "总结内容": analysis_sections["Summary_Expert"],
                    "总结时间": datetime.now().isoformat()
                }
            }
        }
        
        # 提取最终汇总（优先来自总结专家，其次是评审专家的最后一条消息）
        if analysis_sections["Summary_Expert"]:
            self._final_summary = analysis_sections["Summary_Expert"][-1]
        elif analysis_sections["Evaluation_Expert"]:
            self._final_summary = analysis_sections["Evaluation_Expert"][-1]
        
        if progress_callback:
            await progress_callback({
                "type": "tool_streaming",
                "tool_name": "extract_analysis_results",
                "content": "需求分析结果整理完成！",
                "accumulated": f"完成分析{processed_count}条消息，生成结构化需求分析报告",
                "timestamp": datetime.now().isoformat()
            })
        
        logger.info(f"✅ 需求分析结果提取完成")
    
    def _get_analysis_statistics(self) -> Dict[str, Any]:
        """获取需求分析统计信息"""
        stats = {
            "analysis_sections": 0,
            "total_content_length": 0,
            "sections_detail": {}
        }
        
        if "需求分析结果" in self.analysis_results:
            analysis_data = self.analysis_results["需求分析结果"]
            stats["analysis_sections"] = len(analysis_data)
            
            for section_name, section_data in analysis_data.items():
                if isinstance(section_data, dict):
                    content_key = None
                    for key in ["分析内容", "策略内容", "评审内容", "总结内容"]:
                        if key in section_data:
                            content_key = key
                            break
                    
                    if content_key and isinstance(section_data[content_key], list):
                        content_list = section_data[content_key]
                        total_length = sum(len(str(item)) for item in content_list)
                        stats["total_content_length"] += total_length
                        stats["sections_detail"][section_name] = {
                            "items_count": len(content_list),
                            "content_length": total_length,
                            "responsible_agent": section_data.get("分析师") or section_data.get("专家") or section_data.get("评审专家") or section_data.get("总结专家", "Unknown")
                        }
        
        return stats

    def _reset_agent_state(self):
        """重置智能体状态，确保会话隔离"""
        logger.info("🔄 重置需求分析智能体状态，确保会话隔离")
        
        # 重置基础状态
        self.retry_count = 0
        self.analysis_results = {"需求分析结果": {}}
        self._current_decision = None
        self._final_summary = None
        
        # 重置取消状态
        self.is_cancelled = False
        self.cancel_reason = None
        
        # 重新初始化AutoGen团队 - 这是关键步骤，包含新的总结专家
        try:
            self._setup_team()
            logger.info("✅ AutoGen团队重置完成，包含4个智能体：需求分析师、测试专家、评审专家、总结专家")
        except Exception as e:
            logger.error(f"❌ 重置AutoGen团队状态失败: {e}")
            raise
