import json
import os
import sys
from typing import List, Dict, Any, AsyncGenerator, Optional
import asyncio
import time
import logging
from datetime import datetime

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import SelectorGroupChat
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.messages import TextMessage, ModelClientStreamingChunkEvent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo, ModelFamily
from autogen_agentchat.conditions import FunctionalTermination, MaxMessageTermination
from autogen_agentchat.base import Response
from autogen_core.model_context import BufferedChatCompletionContext

# 项目内部导入
from app.core.base_agent import BaseAutoGenAgent
from app.config.autogen_config import autogen_config, TERMINATION_KEYWORDS
from app.core.sse import format_sse_data
from app.agents.common.requirements_utils import get_requirements_from_url  # 使用现有的需求获取工具
from app.agents.prompt.requirements_review_prompt import RequirementsReviewPrompts

# 配置日志
logger = logging.getLogger(__name__)

class RequirementsReviewAgent(BaseAutoGenAgent):
    """
    基于AutoGen的需求评审智能体，模拟互联网产品的需求评审会议
    使用SelectorGroupChat实现多智能体团队协作，包含产品、测试、开发、设计、争议分析等角色
    支持多轮讨论和流式输出
    继承BaseAutoGenAgent以集成项目架构
    """
    
    # 智能体元信息
    agent_id = "requirements_review_agent"
    name = "需求评审会"
    description = "基于AutoGen的专业需求评审智能体，模拟互联网产品需求评审会议，支持多角色协作和多轮讨论"
    icon = ""
    
    def __init__(self):
        """初始化需求评审智能体"""
        super().__init__()
        self.review_result = {"评审结果": {}}
        self.retry_count = 0
        self.max_retries = 2  # 增加重试次数支持多轮讨论
        self._current_decision = None  # 存储当前决策信息
        self._review_summary = None  # 存储评审总结
        self._discussion_round = 0  # 讨论轮次
        
        # 初始化AutoGen组件
        self._initialize_autogen()
    
    def _initialize_autogen(self):
        """初始化AutoGen组件"""
        try:
            # 从配置读取模型参数
            self.model_client = OpenAIChatCompletionClient(
                model= os.getenv("OPENAI_MODEL"),
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_API_BASE"),
                model_info=ModelInfo(
                    vision=True,
                    function_calling=True,
                    json_output=True,
                    family="qwen",
                    structured_output=False
                )
            )
            
            # 设置智能体团队
            self.review_result = {"评审结果": {}}
            self._setup_team()
            
        except Exception as e:
            logger.error(f"AutoGen组件初始化失败: {e}")
            raise
    
    def _setup_team(self):
        """设置智能体团队 - 使用SelectorGroupChat模式支持条件控制"""
        
        # 产品经理
        self.product_manager = AssistantAgent(
            name="Product_Manager",
            model_client=self.model_client,
            model_client_stream=True,  # 启用流式输出
            description="资深产品经理，负责从产品角度评审需求",
            system_message=RequirementsReviewPrompts._prompt_product_manager()
        )
        
        # 开发工程师
        self.developer = AssistantAgent(
            name="Developer",
            model_client=self.model_client,
            model_client_stream=True,  # 启用流式输出
            description="资深开发工程师，负责从技术角度评审需求",
            system_message=RequirementsReviewPrompts._prompt_developer()
        )
        
        # 测试工程师
        self.tester = AssistantAgent(
            name="Tester",
            model_client=self.model_client,
            model_client_stream=True,  # 启用流式输出
            description="资深测试工程师，负责从测试角度评审需求",
            system_message=RequirementsReviewPrompts._prompt_tester()
        )
        
        # UI/UX设计师
        self.designer = AssistantAgent(
            name="Designer",
            model_client=self.model_client,
            model_client_stream=True,  # 启用流式输出
            description="资深UI/UX设计师，负责从设计角度评审需求",
            system_message=RequirementsReviewPrompts._prompt_designer()
        )
        
        # 争议点分析师
        self.controversy_analyst = AssistantAgent(
            name="Controversy_Analyst",
            model_client=self.model_client,
            model_client_stream=True,  # 启用流式输出
            description="项目评审专家和冲突调解师，负责分析和处理评审中的争议点",
            system_message=RequirementsReviewPrompts._prompt_controversy_analyst()
        )
        
        # 评审总结分析师
        self.summary_analyst = AssistantAgent(
            name="Summary_Analyst",
            model_client=self.model_client,
            model_client_stream=True,  # 启用流式输出
            description="项目评审专家，负责整理评审结果形成最终报告",
            system_message=RequirementsReviewPrompts._prompt_summary_analyst()
        )
        
        # 创建团队成员列表
        self.participants = [
            self.product_manager,
            self.developer,
            self.tester,
            self.designer,
            self.controversy_analyst,
            self.summary_analyst
        ]
        
        # 定义智能体选择函数
        def selector_func(messages):
            """根据对话历史和条件逻辑选择下一个发言者，支持多轮讨论"""
            if not messages:
                decision_info = {
                    "reason": "需求评审会议开始",
                    "selected_agent": "Product_Manager",
                    "current_stage": "产品评审阶段",
                    "decision_logic": "评审会议初始化，首先由产品经理开始进行产品层面的需求评审"
                }
                # 存储决策信息供后续使用
                self._current_decision = decision_info
                return "Product_Manager"
            
            last_message = messages[-1]
            last_speaker = getattr(last_message, 'source', '')
            last_content = getattr(last_message, 'content', '')
            
            decision_info = {
                "last_speaker": last_speaker,
                "message_content_preview": last_content[:100] + "..." if len(last_content) > 100 else last_content,
                "current_stage": "",
                "decision_logic": "",
                "reason": "",
                "selected_agent": None,
                "discussion_round": self._discussion_round
            }
            
            # 条件分支逻辑 - 支持多轮讨论的评审流程
            if last_speaker == "Product_Manager":
                if "产品评审完成" in last_content:
                    decision_info.update({
                        "reason": "产品评审完成",
                        "selected_agent": "Developer",
                        "current_stage": "开发评审阶段",
                        "decision_logic": "检测到产品经理说'产品评审完成'，评审流程进入开发技术评审阶段"
                    })
                    self._current_decision = decision_info
                    return "Developer"
                else:
                    decision_info.update({
                        "reason": "产品评审未完成",
                        "selected_agent": "Product_Manager",
                        "current_stage": "产品评审阶段",
                        "decision_logic": "产品经理尚未完成评审，继续由产品经理发言"
                    })
                    self._current_decision = decision_info
                    return "Product_Manager"
                    
            elif last_speaker == "Developer":
                if "开发评审完成" in last_content:
                    decision_info.update({
                        "reason": "开发评审完成",
                        "selected_agent": "Tester",
                        "current_stage": "测试评审阶段",
                        "decision_logic": "检测到开发工程师说'开发评审完成'，评审流程进入测试评审阶段"
                    })
                    self._current_decision = decision_info
                    return "Tester"
                else:
                    decision_info.update({
                        "reason": "开发评审未完成",
                        "selected_agent": "Developer",
                        "current_stage": "开发评审阶段",
                        "decision_logic": "开发工程师尚未完成评审，继续由开发工程师发言"
                    })
                    self._current_decision = decision_info
                    return "Developer"
                 
            elif last_speaker == "Tester":
                if "测试评审完成" in last_content:
                    decision_info.update({
                        "reason": "测试评审完成",
                        "selected_agent": "Designer",
                        "current_stage": "设计评审阶段",
                        "decision_logic": "检测到测试工程师说'测试评审完成'，评审流程进入UI/UX设计评审阶段"
                    })
                    self._current_decision = decision_info
                    return "Designer"
                else:
                    decision_info.update({
                        "reason": "测试评审未完成",
                        "selected_agent": "Tester",
                        "current_stage": "测试评审阶段",
                        "decision_logic": "测试工程师尚未完成评审，继续由测试工程师发言"
                    })
                    self._current_decision = decision_info
                    return "Tester"
                    
            elif last_speaker == "Designer":
                if "设计评审完成" in last_content:
                    decision_info.update({
                        "reason": "设计评审完成",
                        "selected_agent": "Controversy_Analyst",
                        "current_stage": "争议点分析阶段",
                        "decision_logic": "检测到UI/UX设计师说'设计评审完成'，评审流程进入争议点分析阶段"
                    })
                    self._current_decision = decision_info
                    return "Controversy_Analyst"
                else:
                    decision_info.update({
                        "reason": "设计评审未完成",
                        "selected_agent": "Designer",
                        "current_stage": "设计评审阶段",
                        "decision_logic": "UI/UX设计师尚未完成评审，继续由设计师发言"
                    })
                    self._current_decision = decision_info
                    return "Designer"
                    
            elif last_speaker == "Controversy_Analyst":
                # 争议点分析师的复杂逻辑处理
                if "争议分析完成，可以进入总结阶段" in last_content:
                    decision_info.update({
                        "reason": "争议分析完成",
                        "selected_agent": "Summary_Analyst",
                        "current_stage": "评审总结阶段",
                        "decision_logic": "争议点分析师确认无重大争议，进入最终总结阶段"
                    })
                    self._current_decision = decision_info
                    return "Summary_Analyst"
                elif "需要产品经理重新分析" in last_content:
                    self._discussion_round += 1
                    decision_info.update({
                        "reason": "需要产品经理重新分析",
                        "selected_agent": "Product_Manager",
                        "current_stage": f"产品评审阶段(第{self._discussion_round + 1}轮)",
                        "decision_logic": f"争议点分析师要求产品经理重新分析，开始第{self._discussion_round + 1}轮讨论"
                    })
                    self._current_decision = decision_info
                    return "Product_Manager"
                elif "需要开发工程师重新分析" in last_content:
                    self._discussion_round += 1
                    decision_info.update({
                        "reason": "需要开发工程师重新分析",
                        "selected_agent": "Developer",
                        "current_stage": f"开发评审阶段(第{self._discussion_round + 1}轮)",
                        "decision_logic": f"争议点分析师要求开发工程师重新分析，开始第{self._discussion_round + 1}轮讨论"
                    })
                    self._current_decision = decision_info
                    return "Developer"
                elif "需要测试工程师重新分析" in last_content:
                    self._discussion_round += 1
                    decision_info.update({
                        "reason": "需要测试工程师重新分析",
                        "selected_agent": "Tester",
                        "current_stage": f"测试评审阶段(第{self._discussion_round + 1}轮)",
                        "decision_logic": f"争议点分析师要求测试工程师重新分析，开始第{self._discussion_round + 1}轮讨论"
                    })
                    self._current_decision = decision_info
                    return "Tester"
                elif "需要设计师重新分析" in last_content or "需要UI/UX设计师重新分析" in last_content:
                    self._discussion_round += 1
                    decision_info.update({
                        "reason": "需要设计师重新分析",
                        "selected_agent": "Designer",
                        "current_stage": f"设计评审阶段(第{self._discussion_round + 1}轮)",
                        "decision_logic": f"争议点分析师要求设计师重新分析，开始第{self._discussion_round + 1}轮讨论"
                    })
                    self._current_decision = decision_info
                    return "Designer"
                else:
                    # 争议点分析师还在分析中
                    decision_info.update({
                        "reason": "争议分析进行中",
                        "selected_agent": "Controversy_Analyst",
                        "current_stage": "争议点分析阶段",
                        "decision_logic": "争议点分析师继续分析争议"
                    })
                    self._current_decision = decision_info
                    return "Controversy_Analyst"
                    
            elif last_speaker == "Summary_Analyst":
                # 评审总结完成，会议结束
                decision_info.update({
                    "reason": "评审总结完成",
                    "selected_agent": None,
                    "current_stage": "评审会议结束",
                    "decision_logic": "评审总结分析师完成总结报告，需求评审会议结束"
                })
                self._current_decision = decision_info
                return None
            
            # 默认情况
            decision_info.update({
                "reason": "未知状态",
                "selected_agent": None,
                "current_stage": "评审会议结束",
                "decision_logic": "遇到未预期的情况，评审会议结束"
            })
            self._current_decision = decision_info
            return None
        
        # 创建自定义终止条件
        def custom_termination_condition(messages):
            """自定义终止条件：Summary_Analyst完成总结后终止"""
            if not messages:
                return False
            
            last_message = messages[-1]
            last_speaker = getattr(last_message, 'source', '')
            last_content = getattr(last_message, 'content', '')
            
            if last_speaker == "Summary_Analyst":
                if "# 需求评审总结报告" in last_content or "最终评审结论" in last_content:
                    return True
                    
            return False
        
        # 组合终止条件
        termination_condition = (
            FunctionalTermination(custom_termination_condition) |
            MaxMessageTermination(30)  # 增加最大消息数支持多轮讨论
        )
        
        # 创建SelectorGroupChat团队
        self.team = SelectorGroupChat(
            participants=self.participants,
            model_client=self.model_client,
            termination_condition=termination_condition,
            max_turns=15,  # 增加最大轮次支持多轮讨论
            selector_func=selector_func,
            allow_repeated_speaker=True,
            max_selector_attempts=3,
            emit_team_events=True,
            model_client_streaming=True
        )
    
    async def _run_autogen_workflow(self, query: str, context: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """
        运行AutoGen需求评审工作流的核心实现 - 按照轮次机制输出SSE
        评审流程：产品经理→开发工程师→测试工程师→UI/UX设计师→争议点分析师→(可能的多轮讨论)→评审总结
        
        Args:
            query: 用户查询（需求文档内容或URL）
            context: 上下文信息
            
        Yields:
            流式输出的结果数据
        """
        # 重置所有状态 - 确保会话隔离
        self._reset_agent_state()
        
        start_time = time.time()
        
        try:
            # 1. 工作流开始
            yield {
                "type": "agent_start",
                "message": "需求评审会启动，模拟互联网产品需求评审会议",
                "agent_id": self.agent_id,
                "agent_name": self.name,
                "timestamp": datetime.now().isoformat()
            }
            
            # 2. 使用工具获取需求文档内容
            yield {
                "type": "thinking", 
                "message": "正在获取需求文档内容...",
                "timestamp": datetime.now().isoformat()
            }
            
            # 直接调用工具获取需求文档
            processed_requirements = await get_requirements_from_url(query)
            
            yield {
                "type": "tool_result",
                "tool_name": "requirements_tool",
                "result": f"需求文档获取完成，开始多角色评审会议，需求内容：" + processed_requirements[:100] + "...",
                "agent_name": "requirements_tool",
                "timestamp": datetime.now().isoformat()
            }
            
            # 构造团队评审任务
            task = f"""
我们是一个专业的产品需求评审团队，现在要对以下需求文档进行全面评审：

用户输入内容：
{query}

需求文档内容：
{processed_requirements}

【评审会议流程】
请按照以下顺序进行专业评审：
1. 产品经理：从产品角度评审需求的商业价值、用户体验和可行性
2. 开发工程师：从技术角度评审需求的可实现性、复杂度和技术风险  
3. 测试工程师：从测试角度评审需求的可测试性、测试覆盖度和质量风险
4. UI/UX设计师：从设计角度评审需求的用户体验、界面设计和交互合理性
5. 争议点分析师：分析各角色提出的争议点，判断是否需要重新讨论
6. 评审总结分析师：整合各角色意见，形成最终评审报告

【评审要求】
- 每个角色都要根据自己的专业背景进行深入分析
- 要指出具体问题并提供改进建议，如有争议点请明确指出
- 要给出工作量和风险评估
- 争议点分析师会判断争议是否需要重新讨论
- 最后形成markdown格式的综合性评审结论和建议

现在开始需求评审会议！
"""
            
            # 运行团队协作流式处理 - 按轮次处理
            turn_number = 0
            current_speaker = None
            current_turn_messages = []
            streaming_buffer = ""
            is_in_agent_execution = False
            
            async for message in self.team.run_stream(task=task):
                # 检查是否被取消
                if self.is_cancelled:
                    yield {
                        "type": "stopped",
                        "message": f"任务已被取消: {self.cancel_reason}",
                        "timestamp": datetime.now().isoformat()
                    }
                    break
                
                # 1. 处理SelectSpeakerEvent - 轮次开始，选择发言者
                if hasattr(message, 'type') and message.type == 'SelectSpeakerEvent':
                    # 结束上一个智能体的执行（如果有）
                    if is_in_agent_execution and current_speaker:
                        yield {
                            "type": "tool_result",
                            "tool_name": f"{current_speaker}_review_turn_{turn_number}",
                            "result": f"{current_speaker}完成当前阶段评审",
                            "agent_name": current_speaker,
                            "accumulated_content": streaming_buffer,
                            "timestamp": datetime.now().isoformat()
                        }
                        is_in_agent_execution = False
                    
                    # 开始新轮次
                    turn_number += 1
                    selected_speakers = message.content
                    current_speaker = selected_speakers[0] if selected_speakers else None
                    current_turn_messages = []
                    streaming_buffer = ""
                    
                    if current_speaker and current_speaker != "user":
                        # 思考阶段
                        speaker_role_map = {
                            "Product_Manager": "产品经理",
                            "Developer": "开发工程师", 
                            "Tester": "测试工程师",
                            "Designer": "UI/UX设计师",
                            "Controversy_Analyst": "争议点分析师",
                            "Summary_Analyst": "评审总结分析师"
                        }
                        speaker_name = speaker_role_map.get(current_speaker, current_speaker)
                        
                        # 显示讨论轮次信息
                        round_info = f"第{self._discussion_round + 1}轮" if self._discussion_round > 0 else ""
                        
                        yield {
                            "type": "thinking",
                            "message": f"评审角色：{speaker_name}（{current_speaker}），第{turn_number}轮评审{round_info}，开始分析...",
                            "turn_number": turn_number,
                            "discussion_round": self._discussion_round,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        # 决策阶段
                        decision_info = getattr(self, '_current_decision', None) or {
                            "reason": f"选择{speaker_name}进行评审",
                            "current_stage": f"{speaker_name}评审阶段",
                            "decision_logic": f"根据评审会议流程选择{speaker_name}进行第{turn_number}轮评审"
                        }
                        
                        # 工具执行开始
                        yield {
                            "type": "tool_start",
                            "tool_name": f"{current_speaker}_review_turn_{turn_number}",
                            "function_name": f"{speaker_name}专业评审",
                            "parameters": {
                                "评审角色": speaker_name, 
                                "轮次": turn_number,
                                "讨论轮次": self._discussion_round,
                                "阶段": decision_info.get("current_stage", "评审阶段")
                            },
                            "turn_number": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        is_in_agent_execution = True
                
                # 2. 处理ModelClientStreamingChunkEvent - 智能体思考流式输出
                elif isinstance(message, ModelClientStreamingChunkEvent):
                    if current_speaker and current_speaker != "user" and is_in_agent_execution:
                        streaming_buffer += message.content
                        
                        # 流式输出智能体的评审过程
                        yield {
                            "type": "tool_streaming",
                            "tool_name": f"{current_speaker}_review_turn_{turn_number}",
                            "content": message.content,
                            "accumulated": streaming_buffer,
                            "agent_name": current_speaker,
                            "turn_number": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                
                # 3. 处理完整消息 - 智能体完成一轮评审
                elif hasattr(message, 'content') and hasattr(message, 'source'):
                    current_turn_messages.append(message)
                    
                    if message.source != "user" and message.source == current_speaker and is_in_agent_execution:
                        # 智能体完成当前轮次的评审
                        content_str = str(message.content) if not isinstance(message.content, str) else message.content
                        
                        # 检查是否是阶段完成的标志
                        completion_keywords = [
                            "产品评审完成", "开发评审完成", "测试评审完成", "设计评审完成", 
                            "争议分析完成", "# 需求评审总结报告"
                        ]
                        if any(keyword in content_str for keyword in completion_keywords):
                            # 阶段完成
                            yield {
                                "type": "tool_result",
                                "tool_name": f"{current_speaker}_review_turn_{turn_number}",
                                "result": f"{current_speaker}成功完成专业评审",
                                "agent_name": current_speaker,
                                "message_content": content_str[:200] + "..." if len(content_str) > 200 else content_str,
                                "accumulated_content": streaming_buffer,
                                "turn_number": turn_number,
                                "timestamp": datetime.now().isoformat()
                            }
                            
                            is_in_agent_execution = False
                        else:
                            # 智能体还在继续当前轮次的工作
                            yield {
                                "type": "tool_streaming",
                                "tool_name": f"{current_speaker}_review_turn_{turn_number}",
                                "content": f"\n[{current_speaker}继续评审中...]",
                                "accumulated": streaming_buffer + f"\n[继续评审...] ",
                                "agent_name": current_speaker,
                                "turn_number": turn_number,
                                "timestamp": datetime.now().isoformat()
                            }
                
                # 4. 处理TaskResult - 整个评审会议完成
                elif hasattr(message, 'messages'):
                    # 结束最后一个智能体的执行
                    if is_in_agent_execution and current_speaker:
                        yield {
                            "type": "tool_result",
                            "tool_name": f"{current_speaker}_review_turn_{turn_number}",
                            "result": f"{current_speaker}完成最终评审",
                            "agent_name": current_speaker,
                            "accumulated_content": streaming_buffer,
                            "turn_number": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                        is_in_agent_execution = False
                    
                    # 评审会议完成，开始整理评审结果
                    yield {
                        "type": "thinking",
                        "message": f"所有角色评审完成（共{turn_number}轮，{self._discussion_round + 1}轮讨论），正在整理需求评审结果...",
                        "total_turns": turn_number,
                        "total_discussion_rounds": self._discussion_round + 1,
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    yield {
                        "type": "tool_start",
                        "tool_name": "extract_review_results",
                        "function_name": "提取评审结果",
                        "parameters": {"消息数量": len(message.messages), "轮次数": turn_number, "讨论轮次": self._discussion_round + 1},
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    # 提取评审结果（带进度输出）
                    progress_data_list = []
                    
                    async def collect_progress(progress_data):
                        progress_data_list.append(progress_data)
                    
                    await self._extract_review_results_from_messages(message.messages, collect_progress)
                    
                    # 输出收集的进度
                    for progress_data in progress_data_list:
                        yield progress_data
                    
                    yield {
                        "type": "tool_result",
                        "tool_name": "extract_review_results",
                        "result": f"成功提取评审结果，包含{len(self.review_result['评审结果'])}个角色的评审意见",
                        "timestamp": datetime.now().isoformat()
                    }
                    break
    
            # 生成最终结果
            end_time = time.time()
            duration = end_time - start_time
            
            # 生成用户友好的文本总结
            summary_text = self._generate_summary_text()
            
            yield {
                "type": "summary",
                "message": "需求评审会议完成，评审结果如下：",
                "content": summary_text,
                "review_summary": getattr(self, '_review_summary', None),
                "duration": f"{duration:.2f}秒",
                "total_turns": turn_number,
                "total_discussion_rounds": self._discussion_round + 1,
                "raw_result": self.review_result,  # 保留原始JSON数据作为备用
                "timestamp": datetime.now().isoformat()
            }
            
            yield {
                "type": "status",
                "message": f"需求评审会议已完成，形成最终评审报告（共{turn_number}轮评审，{self._discussion_round + 1}轮讨论）",
                "timestamp": datetime.now().isoformat()
            }
            
            yield {
                "type": "complete",
                "message": "需求评审任务完成",
                "success": True,
                "final_result": self.review_result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"AutoGen需求评审工作流执行错误: {e}", exc_info=True)
            yield {
                "type": "error",
                "message": f"评审过程中出现错误: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
            raise
    
    async def _extract_review_results_from_messages(self, messages, progress_callback=None):
        """
        从对话消息中提取需求评审结果
        
        Args:
            messages: 对话消息列表
            progress_callback: 进度回调函数，用于流式输出进度
        """
        logger.info("🔍 正在从评审对话中提取评审结果...")
        
        if progress_callback:
            await progress_callback({
                "type": "tool_streaming",
                "tool_name": "extract_review_results",
                "content": f"开始分析{len(messages)}条评审消息...",
                "accumulated": f"开始分析{len(messages)}条评审消息...",
                "timestamp": datetime.now().isoformat()
            })
        
        processed_count = 0
        found_reviews = 0
        
        # 角色映射
        role_map = {
            "Product_Manager": "产品经理",
            "Developer": "开发工程师", 
            "Tester": "测试工程师",
            "Designer": "UI/UX设计师",
            "Controversy_Analyst": "争议点分析师",
            "Summary_Analyst": "评审总结"
        }
        
        # 遍历消息，提取评审结果
        for i, message in enumerate(messages):
            if hasattr(message, 'content') and hasattr(message, 'source'):
                content = message.content
                source = message.source
                processed_count += 1
                
                # 安全处理content
                if not isinstance(content, str):
                    content = str(content)
                
                # 检查是否是角色评审内容
                if source in role_map and len(content) > 100:
                    role_name = role_map[source]
                    # 支持多轮讨论，保留所有轮次的评审内容
                    if role_name not in self.review_result["评审结果"]:
                        self.review_result["评审结果"][role_name] = []
                    
                    self.review_result["评审结果"][role_name].append({
                        "内容": content,
                        "评审时间": datetime.now().isoformat(),
                        "角色": source,
                        "轮次": i + 1
                    })
                    found_reviews += 1
                    
                    # 特殊处理评审总结（markdown格式）
                    if source == "Summary_Analyst" and "# 需求评审总结报告" in content:
                        self._review_summary = content
                    
                    if progress_callback:
                        await progress_callback({
                            "type": "tool_streaming",
                            "tool_name": "extract_review_results",
                            "content": f"提取到{role_name}的评审意见...",
                            "accumulated": f"已处理{processed_count}/{len(messages)}条消息，发现{found_reviews}个角色评审",
                            "timestamp": datetime.now().isoformat()
                        })
                
                # 定期报告进度
                elif progress_callback and (i + 1) % 5 == 0:
                    await progress_callback({
                        "type": "tool_streaming",
                        "tool_name": "extract_review_results",
                        "content": f"继续分析中... ({i + 1}/{len(messages)})",
                        "accumulated": f"已处理{processed_count}/{len(messages)}条消息，发现{found_reviews}个角色评审",
                        "timestamp": datetime.now().isoformat()
                    })
        
        # 最终统计
        if progress_callback:
            await progress_callback({
                "type": "tool_streaming",
                "tool_name": "extract_review_results",
                "content": f"评审结果提取完成！共收集{found_reviews}个角色评审意见",
                "accumulated": f"完成分析{processed_count}条消息，成功提取{found_reviews}个角色的评审结果",
                "timestamp": datetime.now().isoformat()
            })
        
        logger.info(f"评审结果提取完成，共{found_reviews}个角色评审")
    
    def _generate_summary_text(self) -> str:
        """
        生成用户友好的文本格式评审总结
        
        Returns:
            格式化的评审总结文本
        """
        if not self.review_result.get("评审结果"):
            return "暂无评审结果"
        
        summary_lines = []
        summary_lines.append("需求评审会议总结")
        summary_lines.append("=" * 50)
        summary_lines.append("")
        
        # 角色评审部分
        roles_reviewed = list(self.review_result["评审结果"].keys())
        summary_lines.append(f"参与评审角色：{', '.join(roles_reviewed)} ({len(roles_reviewed)}个角色)")
        summary_lines.append(f"讨论轮次：第{self._discussion_round + 1}轮")
        summary_lines.append("")
        
        # 各角色评审要点
        for role_name, review_data in self.review_result["评审结果"].items():
            summary_lines.append(f"{role_name} 评审要点:")
            summary_lines.append("-" * 30)
            
            if isinstance(review_data, list) and review_data:
                # 取最后一轮评审（最新的）
                latest_review = review_data[-1]
                content = latest_review.get("内容", "")
                
                # 提取关键信息（前300字符）
                if len(content) > 300:
                    key_points = content[:300] + "..."
                else:
                    key_points = content
                
                summary_lines.append(key_points)
                
                if len(review_data) > 1:
                    summary_lines.append(f"（注：该角色共进行了{len(review_data)}轮评审）")
            else:
                summary_lines.append("评审内容获取异常")
                
            summary_lines.append("")
        
        # 评审总结（如果有）
        if hasattr(self, '_review_summary') and self._review_summary:
            summary_lines.append("评审总结报告:")
            summary_lines.append("-" * 30)
            # 如果是markdown格式，保持原格式
            if "# 需求评审总结报告" in self._review_summary:
                summary_lines.append(self._review_summary)
            else:
                # 否则简化显示
                summary_preview = self._review_summary[:500] + "..." if len(self._review_summary) > 500 else self._review_summary
                summary_lines.append(summary_preview)
            summary_lines.append("")
        
        # 会议统计
        summary_lines.append("统计信息:")
        summary_lines.append("-" * 30)
        summary_lines.append(f"• 评审角色数量: {len(roles_reviewed)}个")
        summary_lines.append(f"• 讨论轮次: 第{self._discussion_round + 1}轮")
        summary_lines.append(f"• 评审完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return "\n".join(summary_lines)
    
    def _reset_agent_state(self):
        """重置智能体状态，确保会话隔离"""
        logger.info("重置需求评审智能体状态，确保会话隔离")
        
        # 重置基础状态
        self.retry_count = 0
        self.review_result = {"评审结果": {}}
        self._current_decision = None
        self._review_summary = None
        self._discussion_round = 0  # 重置讨论轮次
        
        # 重置取消状态
        self.is_cancelled = False
        self.cancel_reason = None
        
        # 重新初始化AutoGen团队 - 这是关键步骤
        try:
            self._setup_team()
        except Exception as e:
            logger.error(f"重置AutoGen团队状态失败: {e}")
            raise
