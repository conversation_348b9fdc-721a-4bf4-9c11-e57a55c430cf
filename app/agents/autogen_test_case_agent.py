import json
import os
import sys
from typing import List, Dict, Any, AsyncGenerator, Optional
import asyncio
import time
import logging
from datetime import datetime

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import SelectorGroupChat
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.messages import TextMessage, ModelClientStreamingChunkEvent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo, ModelFamily
from autogen_agentchat.conditions import FunctionalTermination, MaxMessageTermination
from autogen_agentchat.base import Response
from autogen_core.model_context import BufferedChatCompletionContext
# 使用Pydantic解析器进行解析
from app.core.json_parser_pydantic import parse_test_cases_from_text
# 项目内部导入
from app.core.base_agent import BaseAutoGenAgent
from app.config.autogen_config import autogen_config, TERMINATION_KEYWORDS
from app.core.sse import format_sse_data
from app.agents.common.requirements_utils import get_requirements_from_url  # 使用现有的需求获取工具
from app.tools.excel_converter import TestCaseExcelConverter
from app.agents.common.test_case_organizer import TestCaseOrganizer  # 导入编程式整理器
from app.agents.prompt.autogen_test_case_prompt import TestCaseV7Prompts
from app.core.json_parser_pydantic import PydanticJSONParser, parse_test_cases_from_text
# 移除了提示词拦截器导入

# 配置日志
logger = logging.getLogger(__name__)

class AutoGenTestCaseV7Agent(BaseAutoGenAgent):
    """
    基于AutoGen的测试用例生成智能体，支持输入需求文档获取需求进行用例编写和评审
    使用SelectorGroupChat实现多智能体团队协作
    支持流式输出和条件控制机制
    继承BaseAutoGenAgent以集成项目架构
    """
    
    # 智能体元信息
    agent_id = "autogen_test_case_agent"
    name = "测试用例编写"
    description = "基于AutoGen专业测试用例生成智能体，支持多智能体协作和流式输出，支持输入需求文档获取需求进行用例编写和评审"
    icon = ""
    
    def __init__(self):
        """初始化测试用例生成智能体"""
        super().__init__()
        self.test_cases = {"测试用例": []}
        self.retry_count = 0
        self.max_retries = 3
        self._current_decision = None  # 存储当前决策信息
        self._pydantic_stats = None  # 存储Pydantic解析后的详细统计信息
        
        # 🔥 初始化编程式整理器
        self.test_case_organizer = TestCaseOrganizer(
            case_similarity_threshold=0.8,
            module_similarity_threshold=0.8
        )
        
        # 🔥 初始化Excel转换器
        self.excel_converter = TestCaseExcelConverter()
        
        # 移除了提示词拦截器相关日志
        
        # 初始化AutoGen组件
        self._initialize_autogen()
    
    # 初始化AutoGen组件
    def _initialize_autogen(self):
        """初始化AutoGen组件"""
        try:
            # 使用标准的OpenAI客户端
            self.model_client = OpenAIChatCompletionClient(
                model= os.getenv("OPENAI_MODEL"),
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_API_BASE"),
                model_info=ModelInfo(
                    vision=True,
                    function_calling=True,
                    json_output=True,
                    family="qwen",
                    structured_output=False
                )
            )
            
            # 设置智能体团队
            self.test_cases = {"测试用例": []}
            self._setup_team()
            
        except Exception as e:
            logger.error(f"AutoGen组件初始化失败: {e}")
            raise
    
    # 设置智能体团队 - 使用SelectorGroupChat模式支持条件控制
    def _setup_team(self):
        """设置智能体团队 - 使用SelectorGroupChat模式支持条件控制"""
        
        # 需求分析师
        self.requirements_analyst = AssistantAgent(
            name="Requirements_Analyst",
            model_client=OpenAIChatCompletionClient(
                model= os.getenv("OPENAI_MODEL"),
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_API_BASE"),
                model_info=ModelInfo(
                    vision=True,
                    function_calling=True,
                    json_output=True,
                    family="qwen",
                    structured_output=False
                )
            ),
            model_client_stream=True,  # 启用流式输出
            description="资深需求分析师，专注于深入分析需求文档",
            system_message=TestCaseV7Prompts._prompt_requirements_analysis()
        )
        
        # 测试用例设计师
        self.test_case_designer = AssistantAgent(
            name="Test_Case_Designer",
            model_client=OpenAIChatCompletionClient(
                model= os.getenv("OPENAI_MODEL"),
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_API_BASE"),
                model_info=ModelInfo(
                    vision=True,
                    function_calling=True,
                    json_output=True,
                    family="qwen",
                    structured_output=False
                )
            ),
            model_client_stream=True,  # 启用流式输出
            description="测试用例设计师，专注于设计全面的测试用例",
            system_message=TestCaseV7Prompts._prompt_test_case_designer()
        )
        
        # 测试用例审核员
        self.test_reviewer = AssistantAgent(
            name="Test_Reviewer",
            model_client=OpenAIChatCompletionClient(
                model= os.getenv("OPENAI_MODEL"),
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_API_BASE"),
                model_info=ModelInfo(
                    vision=True,
                    function_calling=True,
                    json_output=True,
                    family="qwen",
                    structured_output=False
                )
            ),
            model_client_stream=True,  # 启用流式输出
            description="测试用例审核员，负责审核和优化测试用例的质量",
            system_message=TestCaseV7Prompts._prompt_test_reviewer()
        )
        
        # 🔥 去掉整理员角色，改用编程式整理
        # 创建团队成员列表（只保留3个核心角色）
        self.participants = [
            self.requirements_analyst,
            self.test_case_designer,
            self.test_reviewer
        ]
        
        # 定义智能体选择函数
        def selector_func(messages):
            """根据对话历史和条件逻辑选择下一个发言者"""
            if not messages:
                decision_info = {
                    "reason": "工作流开始",
                    "selected_agent": "Requirements_Analyst",
                    "current_stage": "需求分析阶段",
                    "decision_logic": "工作流初始化，首先由需求分析师开始分析需求文档"
                }
                # 存储决策信息供后续使用
                self._current_decision = decision_info
                return "Requirements_Analyst"
            
            last_message = messages[-1]
            last_speaker = getattr(last_message, 'source', '')
            last_content = getattr(last_message, 'content', '')
            
            decision_info = {
                "last_speaker": last_speaker,
                "message_content_preview": last_content[:100] + "..." if len(last_content) > 100 else last_content,
                "current_stage": "",
                "decision_logic": "",
                "reason": "",
                "selected_agent": None,
                "retry_count": self.retry_count
            }
            
            # 条件分支逻辑
            if last_speaker == "Requirements_Analyst":
                if "需求分析完成" in last_content:
                    decision_info.update({
                        "reason": "需求分析完成",
                        "selected_agent": "Test_Case_Designer",
                        "current_stage": "测试用例设计阶段",
                        "decision_logic": "检测到需求分析师说'需求分析完成'，工作流进入测试用例设计阶段"
                    })
                    self._current_decision = decision_info
                    return "Test_Case_Designer"
                else:
                    decision_info.update({
                        "reason": "需求分析未完成",
                        "selected_agent": "Requirements_Analyst",
                        "current_stage": "需求分析阶段",
                        "decision_logic": "需求分析师尚未完成分析，继续由需求分析师发言"
                    })
                    self._current_decision = decision_info
                    return "Requirements_Analyst"
                    
            elif last_speaker == "Test_Case_Designer":
                if "测试用例设计完成" in last_content or "测试用例优化完成" in last_content:
                    decision_info.update({
                        "reason": "测试用例设计/优化完成",
                        "selected_agent": "Test_Reviewer",
                        "current_stage": "测试用例审核阶段",
                        "decision_logic": "检测到测试设计师完成设计或优化，工作流进入审核阶段"
                    })
                    self._current_decision = decision_info
                    return "Test_Reviewer"
                else:
                    decision_info.update({
                        "reason": "测试用例设计/优化未完成",
                        "selected_agent": "Test_Case_Designer",
                        "current_stage": "测试用例设计阶段",
                        "decision_logic": "测试设计师尚未完成设计或优化，继续由测试设计师发言"
                    })
                    self._current_decision = decision_info
                    return "Test_Case_Designer"
                 
            elif last_speaker == "Test_Reviewer":
                if "需要重新设计" in last_content:
                    self.retry_count += 1
                    if self.retry_count < self.max_retries:
                        decision_info.update({
                            "reason": "审核不通过，需要重新设计",
                            "selected_agent": "Test_Case_Designer",
                            "current_stage": "测试用例重新设计阶段",
                            "decision_logic": f"审核员要求重新设计，当前重试次数: {self.retry_count}/{self.max_retries}"
                        })
                        self._current_decision = decision_info
                        return "Test_Case_Designer"
                    else:
                        decision_info.update({
                            "reason": "达到最大重试次数",
                            "selected_agent": None,
                            "current_stage": "工作流结束",
                            "decision_logic": f"已达到最大重试次数({self.max_retries})，工作流终止"
                        })
                        self._current_decision = decision_info
                        return None
                elif "审核通过" in last_content:
                    # 🔥 审核通过后直接结束，改用编程式整理
                    decision_info.update({
                        "reason": "审核通过，工作流完成",
                        "selected_agent": None,
                        "current_stage": "工作流完成，准备编程式整理",
                        "decision_logic": "审核员确认测试用例质量良好，工作流结束，将使用编程式整理"
                    })
                    self._current_decision = decision_info
                    return None
                else:
                    decision_info.update({
                        "reason": "审核状态未明确",
                        "selected_agent": None,
                        "current_stage": "工作流结束",
                        "decision_logic": "审核员未明确表态，工作流结束"
                    })
                    self._current_decision = decision_info
                    return None
            
            # 默认情况
            decision_info.update({
                "reason": "未知状态",
                "selected_agent": None,
                "current_stage": "工作流结束",
                "decision_logic": "遇到未预期的情况，工作流结束"
            })
            self._current_decision = decision_info
            return None
        
        # 🔥 修改自定义终止条件，审核员通过后终止
        def custom_termination_condition(messages):
            """自定义终止条件：审核员通过或达到最大重试次数时终止"""
            if not messages:
                return False
            
            last_message = messages[-1]
            last_speaker = getattr(last_message, 'source', '')
            last_content = getattr(last_message, 'content', '')
            
            # 🔥 审核员说"审核通过"时终止
            if last_speaker == "Test_Reviewer":
                if "审核通过" in last_content:
                    return True
                    
            return False
        
        # 组合终止条件
        termination_condition = (
            FunctionalTermination(custom_termination_condition) |
            MaxMessageTermination(20)
        )
        
        # 创建SelectorGroupChat团队
        self.team = SelectorGroupChat(
            participants=self.participants,
            model_client=self.model_client,
            termination_condition=termination_condition,
            max_turns=15,
            selector_func=selector_func,
            allow_repeated_speaker=True,
            max_selector_attempts=3,
            emit_team_events=True,
            model_client_streaming=True
        )
    
    # 运行AutoGen工作流的核心实现 - 按照轮次机制输出SSE
    async def _run_autogen_workflow(self, query: str, context: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """
        运行AutoGen工作流的核心实现 - 按照轮次机制输出SSE
        每轮包含：轮次开始→选择发言者→智能体思考→智能体输出→轮次结束
        
        Args:
            query: 用户查询（需求文档内容或URL）
            context: 上下文信息
            
        Yields:
            流式输出的结果数据
        """
        # 重置所有状态 - 确保会话隔离
        self._reset_agent_state()
        
        start_time = time.time()
        
        try:
            # 1. 工作流开始
            yield {
                "type": "agent_start",
                "message": "测试用例生成智能体启动",
                "agent_id": self.agent_id,
                "agent_name": self.name,
                "timestamp": datetime.now().isoformat()
            }
            
            # 2. 使用工具获取需求文档内容
            yield {
                "type": "thinking", 
                "message": "正在获取需求文档内容...",
                "timestamp": datetime.now().isoformat()
            }
            
            # 直接调用工具获取需求文档
            processed_requirements = await get_requirements_from_url(query)
            
            yield {
                "type": "tool_result",
                "tool_name": "requirements_tool",
                "result": f"需求文档获取完成，开始多智能体协作生成测试用例，需求内容：" + processed_requirements[:100] + "...",
                "agent_name": "requirements_tool",
                "timestamp": datetime.now().isoformat()
            }
            # 构造团队协作任务 - 使用处理后的需求文档
            task = f"""
我们是一个专业的测试用例生成团队，现在需要为以下需求文档生成全面的测试用例：

用户输入内容：
{query}

需求文档内容：
{processed_requirements}

请按照SelectorGroupChat智能工作流协作：
1. 需求分析师：深入分析需求文档，识别所有功能模块
2. 测试用例设计师：设计全面的测试用例，支持根据审核反馈进行迭代优化
3. 测试用例审核员：专业审核用例质量，只审核不编写

重要工作流程规则：
- 需求分析师完成分析后说"需求分析完成"
- 测试用例设计师完成初次设计后说"测试用例设计完成"
- 测试用例设计师完成迭代优化后说"测试用例优化完成"
- 测试用例审核员审核通过后说"审核通过"
- 测试用例审核员审核不通过时说"需要重新设计"并提供具体建议

团队将按照条件控制顺序协作：需求分析→用例设计→审核→(不通过则重新设计)→(通过则结束)

最终会使用编程算法自动整理和去重测试用例，确保结果可靠！

现在开始智能协作！
"""
            
            # 运行团队协作流式处理 - 按轮次处理
            turn_number = 0
            current_speaker = None
            current_turn_messages = []
            streaming_buffer = ""
            is_in_agent_execution = False
            
            async for message in self.team.run_stream(task=task):
                # 检查是否被取消
                if self.is_cancelled:
                    yield {
                        "type": "stopped",
                        "message": f"任务已被取消: {self.cancel_reason}",
                        "timestamp": datetime.now().isoformat()
                    }
                    break
                
                # 1. 处理SelectSpeakerEvent - 轮次开始，选择发言者
                if hasattr(message, 'type') and message.type == 'SelectSpeakerEvent':
                    # 结束上一个智能体的执行（如果有）
                    if is_in_agent_execution and current_speaker:
                        yield {
                            "type": "tool_result",
                            "tool_name": f"{current_speaker}_turn_{turn_number}",
                            "result": f"{current_speaker}完成当前阶段工作",
                            "agent_name": current_speaker,
                            "accumulated_content": streaming_buffer,
                            "timestamp": datetime.now().isoformat()
                        }
                        is_in_agent_execution = False
                    
                    # 开始新轮次
                    turn_number += 1
                    selected_speakers = message.content
                    current_speaker = selected_speakers[0] if selected_speakers else None
                    current_turn_messages = []
                    streaming_buffer = ""
                    
                    if current_speaker and current_speaker != "user":
                        # 思考阶段
                        yield {
                            "type": "thinking",
                            "message": f"智能体选择：{current_speaker}, 第{turn_number}轮，开始执行...",
                            "turn_number": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        # 决策阶段
                        decision_info = getattr(self, '_current_decision', None) or {
                            "reason": f"选择{current_speaker}执行",
                            "current_stage": f"{current_speaker}阶段",
                            "decision_logic": f"根据工作流逻辑选择{current_speaker}执行第{turn_number}轮"
                        }
                        
                        # 工具执行开始
                        yield {
                            "type": "tool_start",
                            "tool_name": f"{current_speaker}_turn_{turn_number}",
                            "function_name": f"{current_speaker}专业分析",
                            "parameters": {
                                "智能体": current_speaker, 
                                "轮次": turn_number,
                                "阶段": decision_info.get("current_stage", "分析阶段")
                            },
                            "turn_number": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        is_in_agent_execution = True
                
                # 2. 处理ModelClientStreamingChunkEvent - 智能体思考流式输出
                elif isinstance(message, ModelClientStreamingChunkEvent):
                    if current_speaker and current_speaker != "user" and is_in_agent_execution:
                        streaming_buffer += message.content
                        
                        # 流式输出智能体的思考过程
                        yield {
                            "type": "tool_streaming",
                            "tool_name": f"{current_speaker}_turn_{turn_number}",
                            "content": message.content,
                            "accumulated": streaming_buffer,
                            "agent_name": current_speaker,
                            "turn_number": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                
                # 3. 处理完整消息 - 智能体完成一轮发言
                elif hasattr(message, 'content') and hasattr(message, 'source'):
                    current_turn_messages.append(message)
                    
                    if message.source != "user" and message.source == current_speaker and is_in_agent_execution:
                        # 智能体完成当前轮次的发言
                        content_str = str(message.content) if not isinstance(message.content, str) else message.content
                        
                        # 检查是否是阶段完成的标志
                        if any(keyword in content_str for keyword in ["需求分析完成", "测试用例设计完成", "测试用例优化完成", "审核通过", "测试用例整理完成"]):
                            # 阶段完成
                            yield {
                                "type": "tool_result",
                                "tool_name": f"{current_speaker}_turn_{turn_number}",
                                "result": f"{current_speaker}成功完成专业分析",
                                "agent_name": current_speaker,
                                "message_content": content_str[:200] + "..." if len(content_str) > 200 else content_str,
                                "accumulated_content": streaming_buffer,
                                "turn_number": turn_number,
                                "timestamp": datetime.now().isoformat()
                            }
                        
                            
                            is_in_agent_execution = False
                        else:
                            # 智能体还在继续当前轮次的工作
                            yield {
                                "type": "tool_streaming",
                                "tool_name": f"{current_speaker}_turn_{turn_number}",
                                "content": f"\n[{current_speaker}继续分析中...]",
                                "accumulated": streaming_buffer + f"\n[继续分析...] ",
                                "agent_name": current_speaker,
                                "turn_number": turn_number,
                                "timestamp": datetime.now().isoformat()
                            }
                
                # 4. 处理TaskResult - 整个工作流完成
                elif hasattr(message, 'messages'):
                    # 结束最后一个智能体的执行
                    if is_in_agent_execution and current_speaker:
                        yield {
                            "type": "tool_result",
                            "tool_name": f"{current_speaker}_turn_{turn_number}",
                            "result": f"{current_speaker}完成最终分析",
                            "agent_name": current_speaker,
                            "accumulated_content": streaming_buffer,
                            "turn_number": turn_number,
                            "timestamp": datetime.now().isoformat()
                        }
                        is_in_agent_execution = False
                    
                    # 工作流完成，开始提取和整理测试用例
                    yield {
                        "type": "thinking",
                        "message": f"所有智能体协作完成（共{turn_number}轮），开始提取测试用例并整理...",
                        "total_turns": turn_number,
                        "timestamp": datetime.now().isoformat()
                    }
                
                    
                    yield {
                        "type": "tool_start",
                        "tool_name": "extract_test_cases",
                        "function_name": "提取测试用例",
                        "parameters": {"消息数量": len(message.messages), "轮次数": turn_number},
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    # 提取测试用例（带进度输出）
                    progress_data_list = []
                    
                    async def collect_progress(progress_data):
                        progress_data_list.append(progress_data)
                    
                    # 先提取测试用例
                    await self._extract_test_cases_from_messages(message.messages, collect_progress)
                    
                    # 输出收集的进度
                    for progress_data in progress_data_list:
                        yield progress_data
                    
                    yield {
                        "type": "tool_result",
                        "tool_name": "extract_test_cases",
                        "result": f"成功生成{self._get_test_case_statistics()['total_cases']}个总测试用例",
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    # 🔥 编程式整理测试用例
                    yield {
                        "type": "tool_start",
                        "tool_name": "programmatic_organizer",
                        "function_name": "编程式整理测试用例",
                        "parameters": {"提取到的用例数": self._get_test_case_statistics()['total_cases']},
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    # 🔥 使用专门的整理器类进行编程式整理
                    organized_result = await self.test_case_organizer.organize_test_cases(
                        self.test_cases, collect_progress)
                    
                    # 输出整理进度
                    for progress_data in progress_data_list[len(progress_data_list) - 5:]:  # 只输出最近5条进度
                        yield progress_data
                    
                    # 更新测试用例数据
                    self.test_cases = organized_result
                    
                    yield {
                        "type": "tool_result",
                        "tool_name": "programmatic_organizer",
                        "result": f"最终整理完成，提取{self._get_test_case_statistics()['total_cases']}个测试用例",
                        "timestamp": datetime.now().isoformat()
                    }
                    break
    
            # 生成最终结果
            end_time = time.time()
            duration = end_time - start_time
            
            # 获取Pydantic解析后的详细统计信息
            pydantic_stats = getattr(self, '_pydantic_stats', self._get_test_case_statistics())
            
            yield {
                "type": "summary",
                "message": "测试用例生成任务完成，已通过编程式整理确保质量：",
                "content": self.test_cases,
                "statistics": pydantic_stats,  # 使用Pydantic解析后的详细统计
                "pydantic_validated": hasattr(self, '_pydantic_stats'),  # 标识是否使用了Pydantic验证
                "programmatic_organized": True,  # 标识使用了编程式整理
                "duration": f"{duration:.2f}秒",
                "total_turns": turn_number,
                "timestamp": datetime.now().isoformat()
            }
            
            yield {
                "type": "status",
                "message": f"所有测试用例已成功生成并整理完成（共{turn_number}轮协作）",
                "timestamp": datetime.now().isoformat()
            }

            # 将测试用例转换为Excel格式
            try:
                # 生成带时间戳的文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                excel_filename = f"测试用例_{timestamp}.xlsx"
                
                # 转换为Excel
                excel_file_path = self.excel_converter.convert_json_to_excel(
                    json_data=self.test_cases,
                    filename=excel_filename
                )
                
                # 构建下载URL（假设通过静态文件服务）
                excel_download_url = f"http://localhost:8000/docs/{excel_filename}"
                
                yield {
                    "type": "status",
                    "message": f"测试用例转换为Excel格式成功，下载地址：{excel_download_url}",
                    "download_url": excel_download_url,
                    "filename": excel_filename,
                    "file_path": excel_file_path,
                    "timestamp": datetime.now().isoformat()
                }
                
            except Exception as e:
                logger.error(f"Excel转换失败: {str(e)}")
                yield {
                    "type": "error",
                    "message": f"Excel转换失败: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }


                        
            yield {
                "type": "complete",
                "message": "测试用例生成任务完成",
                "success": True,
                "final_result": self.test_cases,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"工作流执行错误: {e}", exc_info=True)
            yield {
                "type": "error",
                "message": f"生成过程中出现错误: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
            raise
    
    # 从对话消息中提取测试用例 - 优化版：优先使用整理员的最终结果，避免重复
    async def _extract_test_cases_from_messages(self, messages, progress_callback=None):
        """
        从对话消息中提取测试用例 - 优化版：优先使用整理员的最终结果，避免重复
        
        Args:
            messages: 对话消息列表
            progress_callback: 进度回调函数，用于流式输出进度
        """
        logger.info("🔍 正在从对话中提取测试用例...")
        
        if progress_callback:
            await progress_callback({
                "type": "status",
                "tool_name": "extract_test_cases",
                "content": f"开始分析{len(messages)}条对话消息...",
                "accumulated": f"开始分析{len(messages)}条对话消息...",
                "timestamp": datetime.now().isoformat()
            })
        
        processed_count = 0
        fallback_sources = []  # 备用数据源
        
        # 🔥 简化策略：收集设计师和审核员的所有测试用例
        logger.info("🔍 收集测试设计师和审核员的所有测试用例...")
        
        for i, message in enumerate(messages):
            if hasattr(message, 'content') and hasattr(message, 'source'):
                content = message.content
                source = message.source
                processed_count += 1
                
                # 安全处理content
                if not isinstance(content, str):
                    content = str(content)
                
                # 🔥 详细日志输出每个消息的信息
                logger.info(f"🔍 处理消息 {i+1}/{len(messages)}: 来源={source}, 内容长度={len(content)}, 包含JSON={'有' if '{' in content else '无'}")
                
                # 🔥 收集设计师和审核员的数据
                if ('Test_Case_Designer' in source) and '{' in content:
                    fallback_sources.append((source, content))
                    logger.info(f"📝 收集数据源: {source}, 内容长度: {len(content)}")
                    logger.info(f"🔍 数据预览: {content[:100]}...")
        
        # 🔥 整合所有收集到的测试用例
        logger.info(f"🔧 开始整合{len(fallback_sources)}个数据源的测试用例")
        
        if progress_callback:
            await progress_callback({
                "type": "status",
                "tool_name": "extract_test_cases",
                "content": f"收集到{len(fallback_sources)}个数据源，开始整合",
                "accumulated": f"正在整合数据源: {[source for source, _ in fallback_sources]}",
                "timestamp": datetime.now().isoformat()
            })
        
        for source, content in fallback_sources:
            self._integrate_test_case_content(source, content, is_final_organized=False)
            logger.info(f"📝 已整合数据源: {source}")
        
        # 最终统计
        final_stats = self._get_test_case_statistics()
        extraction_summary = {
            "strategy_used": f"原始数据收集({len(fallback_sources)}个数据源)",
            "data_sources_count": len(fallback_sources),
            "final_cases": final_stats['total_cases']
        }
        
        if progress_callback:
            await progress_callback({
                "type": "status",
                "tool_name": "extract_test_cases",
                "content": f"✅ 测试用例提取完成！收集{len(fallback_sources)}个数据源, 共{final_stats['total_cases']}个测试用例",
                "accumulated": f"提取完成: {len(fallback_sources)}个数据源, 最终测试用例: {final_stats['total_cases']}个",
                "timestamp": datetime.now().isoformat()
            })
        
        # 🔥 使用Pydantic解析和验证所有测试用例
        await self._pydantic_parse_and_validate_test_cases(progress_callback)
        
        logger.info(f"✅ 测试用例提取完成，策略: {extraction_summary['strategy_used']}, 共{final_stats['total_cases']}个测试用例")
    
    # 使用Pydantic解析和验证所有测试用例数据
    async def _pydantic_parse_and_validate_test_cases(self, progress_callback=None):
        """
        使用Pydantic解析和验证所有测试用例数据
        将原始的JSON数据转换为标准化的、验证过的格式
        """
        logger.info("🔥 开始使用Pydantic解析和验证测试用例...")
        
        if progress_callback:
            await progress_callback({
                "type": "status",
                "tool_name": "extract_test_cases",
                "content": "开始使用Pydantic进行数据验证和结构化处理...",
                "accumulated": "正在进行数据验证和结构化处理...",
                "timestamp": datetime.now().isoformat()
            })
        
        try:
            # 初始化Pydantic解析器
            parser = PydanticJSONParser()
            
            # 将当前的测试用例数据转换为JSON字符串进行重新解析
            current_data_str = json.dumps(self.test_cases, ensure_ascii=False, indent=2)
            
            if progress_callback:
                await progress_callback({
                    "type": "status",
                    "tool_name": "extract_test_cases", 
                    "content": f"正在解析{len(self.test_cases.get('测试用例', []))}个模块的测试用例数据...",
                    "accumulated": "数据预处理完成，开始Pydantic解析...",
                    "timestamp": datetime.now().isoformat()
                })
            
            # 使用Pydantic解析
            test_suite = parser.parse_test_cases_from_text(current_data_str)
            
            if test_suite:
                # 转换为传统格式并验证
                validated_test_cases = parser.convert_to_legacy_format(test_suite)
                
                # 更新智能体的测试用例数据
                self.test_cases = validated_test_cases
                
                # 生成详细统计信息
                detailed_stats = {
                    "total_cases": test_suite.total_cases,
                    "modules_count": len(test_suite.modules),
                    "modules_detail": []
                }
                
                for module in test_suite.modules:
                    module_detail = {
                        "module_name": module.name,
                        "cases_count": len(module.test_cases),
                        "cases": []
                    }
                    
                    for case in module.test_cases:
                        case_detail = {
                            "name": case.name,
                            "case_id": case.case_id,
                            "test_type": case.test_type,
                            "test_level": case.test_level,
                            "steps_count": len(case.steps)
                        }
                        module_detail["cases"].append(case_detail)
                    
                    detailed_stats["modules_detail"].append(module_detail)
                
                # 存储详细统计信息供summary使用
                self._pydantic_stats = detailed_stats
                
                if progress_callback:
                    await progress_callback({
                        "type": "status",
                        "tool_name": "extract_test_cases",
                        "content": f"✅ Pydantic验证成功！总计{test_suite.total_cases}个测试用例，{len(test_suite.modules)}个模块",
                        "accumulated": f"数据验证完成，生成标准化测试用例{test_suite.total_cases}个",
                        "timestamp": datetime.now().isoformat()
                    })
                
                logger.info(f"✅ Pydantic解析成功，验证了{test_suite.total_cases}个测试用例")
                
            else:
                # 解析失败，保持原有数据
                logger.warning("⚠️ Pydantic解析失败，保持原有数据格式")
                self._pydantic_stats = self._get_test_case_statistics()
                
                if progress_callback:
                    await progress_callback({
                        "type": "tool_streaming",
                        "tool_name": "extract_test_cases",
                        "content": "⚠️ Pydantic解析失败，使用原有数据格式",
                        "accumulated": "保持原有数据格式，继续处理...",
                        "timestamp": datetime.now().isoformat()
                    })
        
        except Exception as e:
            logger.error(f"❌ Pydantic解析过程中出现错误: {e}")
            # 出错时保持原有数据和统计
            self._pydantic_stats = self._get_test_case_statistics()
            
            if progress_callback:
                await progress_callback({
                    "type": "tool_streaming",
                    "tool_name": "extract_test_cases",
                    "content": f"❌ Pydantic解析出错: {str(e)[:100]}",
                    "accumulated": "解析出错，使用备用处理方案...",
                    "timestamp": datetime.now().isoformat()
                })
    
    # 整合测试用例内容 - 优化版：整理员结果直接替换，其他角色结果合并
    def _integrate_test_case_content(self, source: str, content: str, is_final_organized: bool = False):
        """整合测试用例内容 - 优化版：整理员结果直接替换，其他角色结果合并"""
        
        try:            
            parsed_data = parse_test_cases_from_text(content)
            logger.info(f"🔧 解析结果: {parsed_data is not None}, 包含测试用例: {'测试用例' in (parsed_data or {})}")
            
            if parsed_data and "测试用例" in parsed_data:
                # logger.info(f"🔧 解析成功，测试用例数据类型: {type(parsed_data['测试用例'])}")
                # if isinstance(parsed_data["测试用例"], list):
                #     logger.info(f"🔧 解析得到 {len(parsed_data['测试用例'])} 个模块")
                #     for i, module in enumerate(parsed_data["测试用例"][:3]):  # 只显示前3个模块
                #         logger.info(f"🔧 模块{i+1}: {module}")
                
                if is_final_organized:
                    # 🔥 整理员的结果直接替换，因为整理员负责最终的去重和排序
                    old_count = len(self.test_cases.get("测试用例", []))
                    self.test_cases = parsed_data
                    new_count = len(parsed_data.get('测试用例', []))
                    
                    # 计算测试用例总数
                    total_cases = 0
                    for module in parsed_data.get("测试用例", []):
                        for module_name, cases in module.items():
                            case_count = len(cases) if isinstance(cases, list) else 1
                            total_cases += case_count
                            logger.info(f"   📋 模块 '{module_name}': {case_count} 个测试用例")
                    logger.info(f"   🔢 测试用例总数: {total_cases}")
                    
                else:
                    # 🔥 非整理员的结果合并到现有数据中（设计师、审核员）
                    if isinstance(parsed_data["测试用例"], list):
                        old_count = len(self.test_cases["测试用例"])
                        self.test_cases["测试用例"].extend(parsed_data["测试用例"])
                        new_count = len(self.test_cases["测试用例"])
                        
            else:

                if parsed_data:
                    logger.warning(f"   实际键: {list(parsed_data.keys())}")
                
                # 解析失败，只对非最终结果做备用存储
                if not is_final_organized:
                    logger.warning(f"⚠️ 无法解析 {source} 的JSON格式，暂存原始内容等待Pydantic处理")
                    
                    # 创建简单的备用模块
                    module_name = f"{source}的内容"
                    fallback_case = {
                        f"tc:待解析内容": {
                            "to": f"TC_{source.replace('_', '')[:4].upper()}_001",
                            "tt": "功能测试",
                            "ti": "系统测试", 
                            "tp": "系统正常运行",
                            "ts: 原始内容": f"ex:包含{len(content[:500])}字符的原始内容，等待进一步解析"
                        }
                    }
                    self.test_cases["测试用例"].append({module_name: [fallback_case]})
                else:
                    # 整理员的解析失败是严重问题
                    logger.error(f"❌ 整理员 {source} 的内容解析失败，这可能影响最终结果质量")
                    logger.error(f"❌ 原始内容: {content}")
                
        except Exception as e:
            logger.error(f"❌ 处理 {source} 内容时出错: {e}")
            logger.error(f"❌ 错误详情: {str(e)}")
            
            # 只对非最终结果做错误处理
            if not is_final_organized:
                module_name = f"{source}错误恢复"
                error_case = {
                    f"tc:处理异常": {
                        "to": "TC_ERROR_001",
                        "tt": "功能测试",
                        "ti": "系统测试",
                        "tp": "系统正常运行",
                        "ts: 异常处理": f"ex:处理 {source} 时发生异常: {str(e)[:100]}"
                    }
                }
                self.test_cases["测试用例"].append({module_name: [error_case]})
            else:
                # 整理员处理失败，抛出异常让上层处理
                logger.error(f"💥 整理员 {source} 处理失败，这是关键错误")
                raise Exception(f"整理员数据处理失败: {e}")

    # 获取测试用例统计信息 - 降级方案，主要用于Pydantic解析失败时
    def _get_test_case_statistics(self) -> Dict[str, Any]:
        """获取测试用例统计信息 - 降级方案，主要用于Pydantic解析失败时"""
        total_cases = 0
        module_stats = {}
        
        if "测试用例" in self.test_cases and isinstance(self.test_cases["测试用例"], list):
            for module in self.test_cases["测试用例"]:
                for module_name, cases in module.items():
                    case_count = len(cases) if isinstance(cases, list) else 1
                    total_cases += case_count
                    module_stats[module_name] = case_count
        
        return {
            "total_cases": total_cases,
            "module_stats": module_stats,
            "modules_count": len(module_stats),
            "fallback_mode": True  # 标识这是降级统计
        }
    
    # 重置智能体状态，确保会话隔离
    def _reset_agent_state(self):
        """重置智能体状态，确保会话隔离"""
        logger.info("🔄 重置AutoGen智能体状态，确保会话隔离")
        
        # 重置基础状态
        self.retry_count = 0
        self.test_cases = {"测试用例": []}
        self._current_decision = None
        self._pydantic_stats = None  # 重置Pydantic统计信息
        
        # 重置取消状态
        self.is_cancelled = False
        self.cancel_reason = None
        
        # 重新初始化AutoGen团队 - 这是关键步骤
        try:
            self._setup_team()
        except Exception as e:
            logger.error(f"❌ 重置AutoGen团队状态失败: {e}")
            raise