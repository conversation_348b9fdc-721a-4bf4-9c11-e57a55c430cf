import re
import requests
import json
from app.config.external_interface_url_config import convert_json_to_xmind


async def get_requirements_from_url(url_or_text: str) -> str:
    """
    从文本中提取URL地址（如果有），并获取需求文档的 data.content 字段。
    如果没有URL，则直接返回原内容。
    
    Args:
        url_or_text: 包含URL或直接文本内容
        
    Returns:
        str: 需求文档内容或错误信息
    """


    return url_or_text


async def get_convert_json_to_xmind(json_string: str) -> dict:
    """
    通过AI服务将JSON格式字符串转换为xmind格式
    
    Args:
        json_string: JSON格式的字符串
        
    Returns:
        dict: API返回的响应数据
    """

    payload = {
        "json_string": json_string,
        "type": "xmind"
    }
    
    try:
        response = requests.post(convert_json_to_xmind, json=payload, timeout=30)
        response.raise_for_status()  # 检查HTTP错误

          # 解析响应
        response_data = response.json()
        
        # 提取 data.url
        url = response_data["data"]["url"]
        return url

        
    except requests.exceptions.RequestException as e:
        return {"error": f"请求错误: {str(e)}"}
        
    except json.JSONDecodeError:
        return {"error": "JSON解析错误: 无法解析返回的内容"}
        
    except Exception as e:
        return {"error": f"未知错误: {str(e)}"} 
    



