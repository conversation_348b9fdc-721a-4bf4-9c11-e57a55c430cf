from typing import Dict, Any, AsyncGenerator
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser


async def stream_llm_response(
    llm,
    prompt_template: str,
    tool_name: str,
    input_params: Dict[str, Any]
) -> AsyncGenerator[Dict[str, Any], None]:
    """
    通用的LLM流式响应处理器
    
    Args:
        llm: 语言模型实例
        prompt_template: 提示模板字符串
        tool_name: 工具名称
        input_params: 输入参数字典
        
    Yields:
        Dict[str, Any]: 流式响应数据块
    """
    prompt = ChatPromptTemplate.from_template(prompt_template)
    print("--------------------------------")
    print("tool_name:", repr(tool_name), "prompt:", repr(prompt))
    print("input_params:", repr(input_params))
    print("--------------------------------")

    chain = prompt | llm | StrOutputParser()
    
    full_response = ""
    async for chunk in chain.astream(input_params):
        full_response += chunk
        yield {
            "type": "tool_streaming",
            "tool_name": tool_name,
            "content": chunk,
            "accumulated": full_response
        }
    
    yield {
        "type": "tool_complete",
        "tool_name": tool_name,
        "final_result": full_response
    } 