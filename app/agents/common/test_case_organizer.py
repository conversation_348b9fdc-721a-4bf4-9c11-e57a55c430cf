#!/usr/bin/env python3
"""
测试用例编程式整理器
用确定性算法替代大模型整理员，提供可靠的测试用例去重和组织功能
"""

import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

logger = logging.getLogger(__name__)


class TestCaseOrganizer:
    """
    测试用例编程式整理器
    
    功能特性：
    - 智能去重：多维度相似度检测
    - 模块合并：相似模块名自动合并
    - 中文友好：专为中文优化的相似度算法
    - 透明可控：详细日志和进度输出
    """
    
    def __init__(self, 
                 case_similarity_threshold: float = 0.8,
                 module_similarity_threshold: float = 0.8):
        """
        初始化整理器
        
        Args:
            case_similarity_threshold: 测试用例相似度阈值 (0-1)
            module_similarity_threshold: 模块名相似度阈值 (0-1)
        """
        self.case_similarity_threshold = case_similarity_threshold
        self.module_similarity_threshold = module_similarity_threshold
        
    async def organize_test_cases(self, 
                                test_cases_json: Dict[str, Any],
                                progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        整理测试用例的主入口方法
        
        Args:
            test_cases_json: 原始测试用例JSON数据
            progress_callback: 可选的进度回调函数
            
        Returns:
            整理后的测试用例JSON数据
        """
        logger.info("🔧 开始编程式测试用例整理...")
        
        try:
            # 1. 提取所有测试用例
            all_cases = self._extract_all_cases(test_cases_json, progress_callback)
            
            # 2. 智能去重
            unique_cases = await self._deduplicate_test_cases(all_cases, progress_callback)
            
            # 3. 模块重组
            organized_result = await self._reorganize_by_modules(unique_cases, progress_callback)
            
            # 4. 生成报告
            report = self._generate_organization_report(len(all_cases), 
                                                      len(all_cases) - len(unique_cases), 
                                                      organized_result)
            logger.info(f"🔧 整理报告: {report}")
            
            if progress_callback:
                await progress_callback({
                    "type": "status",
                    "tool_name": "programmatic_organizer",
                    "content": f"编程式整理完成！{report}",
                    "accumulated": f"最终结果: {len(organized_result.get('测试用例', []))} 个模块",
                    "timestamp": datetime.now().isoformat()
                })
            
            return organized_result
            
        except Exception as e:
            logger.error(f"❌ 编程式整理失败: {e}")
            if progress_callback:
                await progress_callback({
                    "type": "status",
                    "tool_name": "programmatic_organizer",
                    "content": f"整理失败: {str(e)}",
                    "accumulated": "编程式整理遇到错误，使用原始数据",
                    "timestamp": datetime.now().isoformat()
                })
            return test_cases_json
    
    def _extract_all_cases(self, test_cases_json: Dict[str, Any], 
                          progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """提取所有测试用例"""
        all_cases = []
        original_modules = test_cases_json.get("测试用例", [])
        
        logger.info(f"🔧 开始提取测试用例，原始模块数: {len(original_modules)}")
        
        case_count = 0
        for module in original_modules:
            for module_name, cases in module.items():
                if isinstance(cases, list):
                    for case in cases:
                        case_count += 1
                        case_info = {
                            'module_name': module_name,
                            'case_data': case,
                            'original_index': case_count
                        }
                        all_cases.append(case_info)
        
        logger.info(f"🔧 提取完成，共 {len(all_cases)} 个测试用例")
        return all_cases
    
    async def _deduplicate_test_cases(self, all_cases: List[Dict[str, Any]], 
                                    progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """智能去重测试用例"""
        unique_cases = []
        
        logger.info(f"🔧 开始智能去重，原始用例数: {len(all_cases)}")
        
        if progress_callback:
            await progress_callback({
                "type": "status",
                "tool_name": "programmatic_organizer",
                "content": f"开始去重处理 {len(all_cases)} 个测试用例...",
                "accumulated": f"使用{self.case_similarity_threshold}相似度阈值进行智能去重",
                "timestamp": datetime.now().isoformat()
            })
        
        for i, current_case in enumerate(all_cases):
            is_duplicate = False
            
            # 与已保留的用例进行相似度比较
            for existing_case in unique_cases:
                if self._are_cases_similar(current_case, existing_case):
                    is_duplicate = True
                    current_name, _ = self._extract_case_details(current_case['case_data'])
                    existing_name, _ = self._extract_case_details(existing_case['case_data'])
                    logger.info(f"🔧 发现相似用例，已去除: '{current_name[:30]}...' (与 '{existing_name[:30]}...' 相似)")
                    break
            
            if not is_duplicate:
                unique_cases.append(current_case)
            
            # 进度输出
            if (i + 1) % 10 == 0 or i == len(all_cases) - 1:
                logger.info(f"🔧 去重进度: {i + 1}/{len(all_cases)}, 保留: {len(unique_cases)}")
                
                if progress_callback and (i + 1) % 20 == 0:
                    await progress_callback({
                        "type": "status",
                        "tool_name": "programmatic_organizer",
                        "content": f"去重进度: {i + 1}/{len(all_cases)}, 保留: {len(unique_cases)}",
                        "accumulated": f"已处理 {i + 1} 个用例，保留 {len(unique_cases)} 个",
                        "timestamp": datetime.now().isoformat()
                    })
        
        removed_count = len(all_cases) - len(unique_cases)
        logger.info(f"🔧 智能去重完成，移除 {removed_count} 个相似用例，保留 {len(unique_cases)} 个")
        
        if progress_callback:
            await progress_callback({
                "type": "status",
                "tool_name": "programmatic_organizer",
                "content": f"去重完成，移除 {removed_count} 个重复用例",
                "accumulated": f"保留 {len(unique_cases)} 个去重后的测试用例，开始重新排序...",
                "timestamp": datetime.now().isoformat()
            })
        
        return unique_cases
    
    async def _reorganize_by_modules(self, unique_cases: List[Dict[str, Any]], 
                                   progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """按模块重新组织测试用例"""
        modules_dict = {}
        normalized_modules = {}  # 标准化模块名映射
        
        logger.info(f"🔧 开始模块重组，用例数: {len(unique_cases)}")
        
        for case_info in unique_cases:
            original_module_name = case_info['module_name']
            case_data = case_info['case_data']
            
            # 查找相似的模块名
            target_module = self._find_similar_module(original_module_name, normalized_modules)
            
            if target_module:
                # 使用现有的相似模块
                if target_module not in modules_dict:
                    modules_dict[target_module] = []
                modules_dict[target_module].append(case_data)
                logger.info(f"🔧 模块名合并: '{original_module_name}' → '{target_module}'")
            else:
                # 创建新模块
                if original_module_name not in modules_dict:
                    modules_dict[original_module_name] = []
                modules_dict[original_module_name].append(case_data)
                normalized_modules[original_module_name] = original_module_name
        
        # 转换为期望的格式，并按模块名称倒序排列
        organized_modules = []
        for module_name in sorted(modules_dict.keys(), reverse=True):
            cases = modules_dict[module_name]
            organized_modules.append({module_name: cases})
        
        logger.info(f"🔧 模块重组完成，最终模块数: {len(organized_modules)}")
        
        if progress_callback:
            await progress_callback({
                "type": "status",
                "tool_name": "programmatic_organizer",
                "content": f"排序完成，生成 {len(organized_modules)} 个模块",
                "accumulated": f"模块重组完成，最终 {len(organized_modules)} 个模块",
                "timestamp": datetime.now().isoformat()
            })
        
        return {"测试用例": organized_modules}
    
    def _are_cases_similar(self, case1_info: Dict[str, Any], case2_info: Dict[str, Any]) -> bool:
        """判断两个测试用例是否相似"""
        case1 = case1_info['case_data']
        case2 = case2_info['case_data']
        
        # 提取用例信息
        case1_name, case1_details = self._extract_case_details(case1)
        case2_name, case2_details = self._extract_case_details(case2)
        
        if not case1_details or not case2_details:
            return False
        
        # 计算相似度
        similarity_score = 0.0
        total_weight = 0.0
        
        # 1. 用例名称相似度 (权重: 0.3)
        name_similarity = self._calculate_text_similarity(case1_name, case2_name)
        similarity_score += name_similarity * 0.3
        total_weight += 0.3
        
        # 2. 测试类型相似度 (权重: 0.2)
        type1 = case1_details.get('tt', '')
        type2 = case2_details.get('tt', '')
        type_similarity = 1.0 if type1.lower() == type2.lower() else 0.0
        similarity_score += type_similarity * 0.2
        total_weight += 0.2
        
        # 3. 前置条件相似度 (权重: 0.2)
        precond1 = case1_details.get('tp', '')
        precond2 = case2_details.get('tp', '')
        precond_similarity = self._calculate_text_similarity(precond1, precond2)
        similarity_score += precond_similarity * 0.2
        total_weight += 0.2
        
        # 4. 测试步骤相似度 (权重: 0.3)
        steps1 = self._extract_test_steps(case1_details)
        steps2 = self._extract_test_steps(case2_details)
        steps_similarity = self._calculate_steps_similarity(steps1, steps2)
        similarity_score += steps_similarity * 0.3
        total_weight += 0.3
        
        final_similarity = similarity_score / total_weight if total_weight > 0 else 0.0
        
        logger.debug(f"🔍 用例相似度分析: '{case1_name[:20]}...' vs '{case2_name[:20]}...' = {final_similarity:.3f}")
        
        return final_similarity >= self.case_similarity_threshold
    
    def _extract_case_details(self, case_data: Any) -> tuple:
        """提取用例详细信息"""
        if not isinstance(case_data, dict):
            return "", {}
        
        for key, value in case_data.items():
            if key.startswith('tc:'):
                case_name = key[3:].strip()
                if isinstance(value, dict):
                    return case_name, value
        
        return "", {}
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（基于字符和词汇重叠）- 中文友好版"""
        if not text1 or not text2:
            return 0.0
        
        text1_lower = text1.lower().strip()
        text2_lower = text2.lower().strip()
        
        # 1. 完全相同
        if text1_lower == text2_lower:
            return 1.0
        
        # 2. 包含关系
        if text1_lower in text2_lower or text2_lower in text1_lower:
            shorter = min(len(text1_lower), len(text2_lower))
            longer = max(len(text1_lower), len(text2_lower))
            return shorter / longer
        
        # 3. 字符级别相似度（适合中文）
        char_similarity = self._calculate_char_similarity(text1_lower, text2_lower)
        
        # 4. 词汇级别相似度
        words1 = set(text1_lower.split())
        words2 = set(text2_lower.split())
        
        if words1 and words2:
            intersection = words1.intersection(words2)
            union = words1.union(words2)
            word_similarity = len(intersection) / len(union) if union else 0.0
        else:
            word_similarity = 0.0
        
        # 综合相似度：字符相似度权重0.7，词汇相似度权重0.3
        final_similarity = char_similarity * 0.7 + word_similarity * 0.3
        
        return final_similarity
    
    def _calculate_char_similarity(self, text1: str, text2: str) -> float:
        """计算字符级别相似度（最长公共子序列）"""
        if not text1 or not text2:
            return 0.0
        
        # 使用最长公共子序列算法
        len1, len2 = len(text1), len(text2)
        dp = [[0] * (len2 + 1) for _ in range(len1 + 1)]
        
        for i in range(1, len1 + 1):
            for j in range(1, len2 + 1):
                if text1[i-1] == text2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])
        
        lcs_length = dp[len1][len2]
        max_length = max(len1, len2)
        
        return lcs_length / max_length if max_length > 0 else 0.0
    
    def _extract_test_steps(self, case_details: Dict[str, Any]) -> List[str]:
        """提取测试步骤"""
        steps = []
        for key, value in case_details.items():
            if key.startswith('ts:'):
                steps.append(f"{key}_{value}")
        return steps
    
    def _calculate_steps_similarity(self, steps1: List[str], steps2: List[str]) -> float:
        """计算测试步骤相似度"""
        if not steps1 and not steps2:
            return 1.0
        if not steps1 or not steps2:
            return 0.0
        
        # 计算步骤内容的重叠度
        total_similarity = 0.0
        for step1 in steps1:
            max_step_similarity = 0.0
            for step2 in steps2:
                step_sim = self._calculate_text_similarity(step1, step2)
                max_step_similarity = max(max_step_similarity, step_sim)
            total_similarity += max_step_similarity
        
        return total_similarity / len(steps1) if steps1 else 0.0
    
    def _find_similar_module(self, module_name: str, existing_modules: Dict[str, str]) -> Optional[str]:
        """查找相似的模块名"""
        if not existing_modules:
            return None
        
        module_name_lower = module_name.lower().strip()
        
        for existing_name, normalized_name in existing_modules.items():
            existing_lower = existing_name.lower().strip()
            
            # 1. 完全相同
            if module_name_lower == existing_lower:
                return normalized_name
            
            # 2. 包含关系（一个是另一个的子字符串）
            if module_name_lower in existing_lower or existing_lower in module_name_lower:
                # 选择较短的作为标准名称（更通用）
                shorter_name = existing_name if len(existing_name) <= len(module_name) else module_name
                return shorter_name
            
            # 3. 字符相似度高
            similarity = self._calculate_text_similarity(module_name, existing_name)
            if similarity >= self.module_similarity_threshold:
                logger.info(f"🔧 发现相似模块: '{module_name}' vs '{existing_name}' = {similarity:.3f}")
                return normalized_name
        
        return None
    
    def _generate_organization_report(self, original_count: int, removed_count: int, 
                                    final_result: Dict[str, Any]) -> str:
        """生成整理报告"""
        final_count = 0
        module_count = len(final_result.get("测试用例", []))
        
        for module in final_result.get("测试用例", []):
            for module_name, cases in module.items():
                final_count += len(cases) if isinstance(cases, list) else 1
        
        return f"原始用例: {original_count}, 去重移除: {removed_count}, 最终用例: {final_count}, 模块数: {module_count}"
    
    def get_statistics(self, test_cases_json: Dict[str, Any]) -> Dict[str, Any]:
        """获取测试用例统计信息"""
        total_cases = 0
        module_stats = {}
        
        if "测试用例" in test_cases_json and isinstance(test_cases_json["测试用例"], list):
            for module in test_cases_json["测试用例"]:
                for module_name, cases in module.items():
                    case_count = len(cases) if isinstance(cases, list) else 1
                    total_cases += case_count
                    module_stats[module_name] = case_count
        
        return {
            "total_cases": total_cases,
            "module_stats": module_stats,
            "modules_count": len(module_stats),
            "organizer_version": "programmatic_v1.0"
        }


# 便捷函数
async def organize_test_cases(test_cases_json: Dict[str, Any],
                            case_similarity_threshold: float = 0.6,
                            module_similarity_threshold: float = 0.5,
                            progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    便捷的测试用例整理函数
    
    Args:
        test_cases_json: 原始测试用例JSON数据
        case_similarity_threshold: 测试用例相似度阈值
        module_similarity_threshold: 模块名相似度阈值
        progress_callback: 可选的进度回调函数
        
    Returns:
        整理后的测试用例JSON数据
    """
    organizer = TestCaseOrganizer(
        case_similarity_threshold=case_similarity_threshold,
        module_similarity_threshold=module_similarity_threshold
    )
    
    return await organizer.organize_test_cases(test_cases_json, progress_callback) 