from fastapi import APIRouter, Request, Depends, Header, UploadFile, File, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
import json
import asyncio
import logging
from datetime import datetime
import os
import tempfile
from pathlib import Path

from app.core.agent_manager import Agent<PERSON>anager
from app.core.sse import sse_stream

# 配置日志记录器
logger = logging.getLogger(__name__)

router = APIRouter()

class AgentRequest(BaseModel):
    agent_id: str
    query: str
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None

class StopSessionRequest(BaseModel):
    session_id: str
    user_id: Optional[str] = None
    reason: Optional[str] = "用户主动停止"

class SwitchAgentRequest(BaseModel):
    session_id: str
    new_agent_id: str
    user_id: Optional[str] = None

class FileParseResponse(BaseModel):
    success: bool
    content: str
    filename: str
    file_type: str
    message: Optional[str] = None

# 支持的文件类型和解析器
SUPPORTED_FILE_TYPES = {
    '.txt': 'text/plain',
    '.md': 'text/markdown',
    '.json': 'application/json',
    '.csv': 'text/csv',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.pdf': 'application/pdf',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
}

async def parse_file_content(file: UploadFile) -> FileParseResponse:
    """解析上传的文件内容"""
    try:
        # 获取文件扩展名
        file_extension = Path(file.filename).suffix.lower()
        
        if file_extension not in SUPPORTED_FILE_TYPES:
            return FileParseResponse(
                success=False,
                content="",
                filename=file.filename,
                file_type=file_extension,
                message=f"不支持的文件类型: {file_extension}"
            )
        
        # 读取文件内容
        content = await file.read()
        
        # 根据文件类型解析内容
        if file_extension in ['.txt', '.md']:
            # 文本文件直接解码
            text_content = content.decode('utf-8')
            
        elif file_extension == '.json':
            # JSON文件解析
            json_data = json.loads(content.decode('utf-8'))
            text_content = json.dumps(json_data, indent=2, ensure_ascii=False)
            
        elif file_extension == '.csv':
            # CSV文件解析
            import pandas as pd
            import io
            
            df = pd.read_csv(io.StringIO(content.decode('utf-8')))
            text_content = f"CSV文件内容：\n{df.to_string()}"
            
        elif file_extension == '.docx':
            # Word文档解析
            try:
                from docx import Document
                import io
                
                doc = Document(io.BytesIO(content))
                paragraphs = [p.text for p in doc.paragraphs if p.text.strip()]
                text_content = '\n'.join(paragraphs)
            except ImportError:
                return FileParseResponse(
                    success=False,
                    content="",
                    filename=file.filename,
                    file_type=file_extension,
                    message="缺少docx解析库，请安装python-docx"
                )
                
        elif file_extension == '.pdf':
            # PDF文件解析
            try:
                import PyPDF2
                import io
                
                pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
                text_content = ""
                for page in pdf_reader.pages:
                    text_content += page.extract_text() + "\n"
            except ImportError:
                return FileParseResponse(
                    success=False,
                    content="",
                    filename=file.filename,
                    file_type=file_extension,
                    message="缺少PDF解析库，请安装PyPDF2"
                )
                
        elif file_extension == '.xlsx':
            # Excel文件解析
            try:
                import pandas as pd
                import io
                
                df = pd.read_excel(io.BytesIO(content))
                text_content = f"Excel文件内容：\n{df.to_string()}"
            except ImportError:
                return FileParseResponse(
                    success=False,
                    content="",
                    filename=file.filename,
                    file_type=file_extension,
                    message="缺少Excel解析库，请安装pandas和openpyxl"
                )
        else:
            return FileParseResponse(
                success=False,
                content="",
                filename=file.filename,
                file_type=file_extension,
                message=f"暂不支持解析 {file_extension} 文件"
            )
        
        # 限制内容长度（防止过长）
        if len(text_content) > 50000:  # 50KB限制
            text_content = text_content[:50000] + "\n\n[内容过长，已截断...]"
        
        return FileParseResponse(
            success=True,
            content=text_content,
            filename=file.filename,
            file_type=file_extension,
            message="文件解析成功"
        )
        
    except Exception as e:
        logger.error(f"文件解析失败: {str(e)}")
        return FileParseResponse(
            success=False,
            content="",
            filename=file.filename,
            file_type=file_extension,
            message=f"文件解析失败: {str(e)}"
        )

@router.post("/upload-file", response_model=FileParseResponse)
async def upload_and_parse_file(file: UploadFile = File(...)):
    """上传文件并解析内容"""
    logger.info(f"📁 接收到文件上传请求: {file.filename}")
    logger.info(f"   - 文件大小: {file.size} bytes")
    logger.info(f"   - 文件类型: {file.content_type}")
    
    # 检查文件大小（限制为10MB）
    if file.size > 50 * 1024 * 1024:
        logger.warning(f"文件 {file.filename} 过大: {file.size} bytes")
        raise HTTPException(status_code=413, detail="文件大小不能超过5MB")
    
    # 解析文件内容
    result = await parse_file_content(file)
    
    if result.success:
        logger.info(f"✅ 文件 {file.filename} 解析成功，内容长度: {len(result.content)} 字符")
    else:
        logger.error(f"❌ 文件 {file.filename} 解析失败: {result.message}")
    
    return result

@router.get("/supported-file-types")
async def get_supported_file_types():
    """获取支持的文件类型列表"""
    return {
        "supported_types": list(SUPPORTED_FILE_TYPES.keys()),
        "type_descriptions": {
            '.txt': '文本文件',
            '.md': 'Markdown文件',
            '.json': 'JSON文件',
            '.csv': 'CSV文件',
            '.docx': 'Word文档',
            '.pdf': 'PDF文档',
            '.xlsx': 'Excel文件'
        }
    }

@router.get("/list")
async def list_agents():
    """获取所有可用智能体列表"""
    logger.info("🔍 获取智能体列表请求")
    agent_manager = AgentManager()
    agents = agent_manager.list_agents()
    logger.info(f"✅ 成功返回 {len(agents)} 个智能体")
    return {"agents": agents}

@router.post("/run")
async def run_agent(request: AgentRequest):
    """运行智能体并以SSE方式返回结果"""
    # 记录请求开始
    start_time = datetime.now()
    request_id = f"{request.agent_id}_{int(start_time.timestamp())}"
    
    logger.info(f"🚀 [请求ID: {request_id}] 开始处理智能体运行请求")
    logger.info(f"📋 [请求ID: {request_id}] 请求参数:")
    logger.info(f"   - 智能体ID: {request.agent_id}")
    logger.info(f"   - 用户查询: {request.query}")
    logger.info(f"   - 会话ID: {request.session_id}")
    logger.info(f"   - 用户ID: {request.user_id}")
    logger.info(f"   - 上下文: {request.context}")
    
    agent_manager = AgentManager()
    
    # 创建一个异步生成器来正确处理协程
    async def run_agent_stream():
        try:
            logger.info(f"🔧 [请求ID: {request_id}] 创建智能体管理器实例")
            
            # 记录智能体运行开始
            logger.info(f"▶️ [请求ID: {request_id}] 开始运行智能体")
            
            result_count = 0
            async for result in agent_manager.run_agent(
                agent_id=request.agent_id,
                query=request.query,
                session_id=request.session_id,
                user_id=request.user_id,
                context=request.context
            ):
                result_count += 1
                result_type = result.get("type", "unknown")
                
                # 记录关键结果类型
                if result_type in ["agent_start", "session_info"]:
                    logger.info(f"[请求ID: {request_id}] 智能体开始: {result}")
                elif result_type == "thinking":
                    logger.debug(f"[请求ID: {request_id}] 智能体思考: {result.get('message', '')}")
                elif result_type == "decision":
                    logger.info(f"[请求ID: {request_id}] 智能体决策: {result.get('content', '')[:100]}...")
                elif result_type == "tool_start":
                    logger.info(f"[请求ID: {request_id}] 开始执行功能: {result.get('function_name')} 参数: {result.get('parameters')}")
                elif result_type == "tool_result":
                    logger.info(f"[请求ID: {request_id}] 功能执行完成: {result.get('function_name')}")
                    logger.debug(f"[请求ID: {request_id}] 功能结果: {str(result.get('result', ''))[:200]}...")
                elif result_type == "error":
                    logger.error(f"[请求ID: {request_id}] 智能体执行错误: {result.get('message')}")
                elif result_type == "stopped":
                    logger.warning(f"[请求ID: {request_id}] 智能体被停止: {result.get('message')}")
                elif result_type == "summary":
                    logger.info(f"[请求ID: {request_id}] 生成摘要内容")
                elif result_type == "status":
                    logger.info(f"[请求ID: {request_id}] 状态更新: {result.get('message')}")
                
                yield result
            
            # 记录执行完成
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"[请求ID: {request_id}] 智能体执行完成")
            logger.info(f"[请求ID: {request_id}] 执行统计:")
            logger.info(f"   - 总耗时: {duration:.2f}秒")
            logger.info(f"   - 返回结果数: {result_count}")
            
        except Exception as e:
            logger.error(f"[请求ID: {request_id}] 智能体执行过程中发生异常: {str(e)}")
            logger.error(f"[请求ID: {request_id}] 异常详情:", exc_info=True)
            
            # 返回错误信息
            yield {
                "type": "error",
                "message": f"智能体执行过程中发生异常: {str(e)}",
                "request_id": request_id,
                "timestamp": datetime.now().isoformat()
            }
    
    try:
        logger.info(f"[请求ID: {request_id}] 创建SSE流式响应")
        return StreamingResponse(
            sse_stream(run_agent_stream()),
            media_type="text/event-stream"
        )
    except Exception as e:
        logger.error(f"[请求ID: {request_id}] 创建流式响应时发生错误: {str(e)}")
        raise

@router.post("/stop")
async def stop_session(request: StopSessionRequest):
    """停止指定会话的智能体运行"""
    logger.info(f"停止会话请求: {request.session_id}")
    logger.info(f"   - 用户ID: {request.user_id}")
    logger.info(f"   - 停止原因: {request.reason}")
    
    agent_manager = AgentManager()
    result = agent_manager.stop_session(
        session_id=request.session_id,
        user_id=request.user_id,
        reason=request.reason
    )
    
    if result.get("success"):
        logger.info(f"会话 {request.session_id} 停止成功")
    else:
        logger.warning(f"会话 {request.session_id} 停止失败: {result.get('message')}")
    
    return result

@router.post("/resume")
async def resume_session(request: StopSessionRequest):
    """恢复指定会话"""
    logger.info(f"恢复会话请求: {request.session_id} (用户: {request.user_id})")
    
    agent_manager = AgentManager()
    result = agent_manager.resume_session(
        session_id=request.session_id,
        user_id=request.user_id
    )
    
    if result.get("success"):
        logger.info(f"会话 {request.session_id} 恢复成功")
    else:
        logger.warning(f"会话 {request.session_id} 恢复失败: {result.get('message')}")
    
    return result

@router.post("/switch")
async def switch_agent(request: SwitchAgentRequest):
    """在现有会话中切换智能体"""
    logger.info(f"切换智能体请求:")
    logger.info(f"   - 会话ID: {request.session_id}")
    logger.info(f"   - 新智能体ID: {request.new_agent_id}")
    logger.info(f"   - 用户ID: {request.user_id}")
    
    agent_manager = AgentManager()
    result = await agent_manager.switch_agent(
        session_id=request.session_id,
        new_agent_id=request.new_agent_id,
        user_id=request.user_id
    )
    
    if result.get("success"):
        logger.info(f"智能体切换成功: {result.get('old_agent_id')} -> {result.get('new_agent_id')}")
    else:
        logger.warning(f"智能体切换失败: {result.get('message')}")
    
    return result

@router.get("/sessions")
async def list_sessions(user_id: Optional[str] = None):
    """获取会话列表"""
    logger.info(f"获取会话列表请求 (用户ID: {user_id})")
    
    agent_manager = AgentManager()
    if user_id:
        sessions = agent_manager.get_user_sessions(user_id)
        logger.info(f"返回用户 {user_id} 的 {len(sessions)} 个会话")
    else:
        sessions = agent_manager.list_sessions()
        logger.info(f"返回所有 {len(sessions)} 个会话")
    
    return {"sessions": sessions}

@router.get("/sessions/{session_id}")
async def get_session_info(session_id: str, user_id: Optional[str] = None):
    """获取指定会话的详细信息"""
    logger.info(f"🔍 获取会话信息: {session_id} (用户: {user_id})")
    
    agent_manager = AgentManager()
    session_info = agent_manager.get_session_info(session_id)
    
    if not session_info:
        logger.warning(f"会话 {session_id} 不存在")
        return {"error": f"会话 {session_id} 不存在"}
    
    # 验证用户权限
    if user_id and session_info.get("user_id") != user_id:
        logger.warning(f"用户 {user_id} 无权限访问会话 {session_id}")
        return {"error": "无权限访问此会话"}
    
    logger.info(f"成功获取会话 {session_id} 信息")
    return session_info

@router.delete("/sessions/{session_id}")
async def terminate_session(session_id: str, user_id: Optional[str] = None):
    """终止指定会话"""
    logger.info(f"终止会话请求: {session_id} (用户: {user_id})")
    
    agent_manager = AgentManager()
    
    # 先验证用户权限
    session_info = agent_manager.get_session_info(session_id)
    if not session_info:
        logger.warning(f"要终止的会话 {session_id} 不存在")
        return {"success": False, "message": f"会话 {session_id} 不存在"}
    
    if user_id and session_info.get("user_id") != user_id:
        logger.warning(f"用户 {user_id} 无权限终止会话 {session_id}")
        return {"success": False, "message": "无权限终止此会话"}
    
    success = agent_manager.terminate_session(session_id)
    
    if success:
        logger.info(f"会话 {session_id} 终止成功")
    else:
        logger.error(f"会话 {session_id} 终止失败")
    
    return {
        "success": success,
        "message": f"会话 {session_id} {'已终止' if success else '终止失败'}"
    } 