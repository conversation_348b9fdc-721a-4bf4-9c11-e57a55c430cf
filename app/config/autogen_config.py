import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# 加载.env文件
load_dotenv(os.path.join(os.path.dirname(__file__), '.env'))  # 尝试从config目录加载
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env'))  # 尝试从app目录加载
load_dotenv()  # 尝试从项目根目录加载

@dataclass
class AutoGenConfig:
    """AutoGen配置类"""
    
    # OpenAI配置
    openai_api_key: str = ""
    openai_model: str = ""
    openai_base_url: Optional[str] = ""
    
    # 智能体配置
    max_round: int = 10
    timeout: int = 300  # 5分钟超时
    
    # 流式输出配置
    stream_delay: float = 0.1  # 流式输出延迟
    
    # 🔥 **新增：日志控制配置**
    autogen_log_level: str = "WARNING"  # AutoGen日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
    suppress_autogen_logs: bool = True  # 是否完全抑制AutoGen内部日志
    show_autogen_errors_only: bool = True  # 是否只显示AutoGen错误日志
    
    @classmethod
    def from_env(cls) -> 'AutoGenConfig':
        """从环境变量创建配置"""
        return cls(
            openai_api_key=os.getenv("OPENAI_API_KEY", ""),
            openai_model=os.getenv("AUTOGEN_MODEL", "gpt-4o-mini"),
            openai_base_url=os.getenv("OPENAI_BASE_URL"),
            max_round=int(os.getenv("AUTOGEN_MAX_ROUND", "10")),
            timeout=int(os.getenv("AUTOGEN_TIMEOUT", "300")),
            stream_delay=float(os.getenv("AUTOGEN_STREAM_DELAY", "0.1")),
            autogen_log_level=os.getenv("AUTOGEN_LOG_LEVEL", "WARNING"),
            suppress_autogen_logs=os.getenv("AUTOGEN_SUPPRESS_LOGS", "True") == "True",
            show_autogen_errors_only=os.getenv("AUTOGEN_SHOW_ERRORS_ONLY", "True") == "True"
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "openai_api_key": self.openai_api_key,
            "openai_model": self.openai_model,
            "openai_base_url": self.openai_base_url,
            "max_round": self.max_round,
            "timeout": self.timeout,
            "stream_delay": self.stream_delay,
            "autogen_log_level": self.autogen_log_level,
            "suppress_autogen_logs": self.suppress_autogen_logs,
            "show_autogen_errors_only": self.show_autogen_errors_only
        }
    
    def is_valid(self) -> bool:
        """检查配置是否有效"""
        return bool(self.openai_api_key and self.openai_api_key != "your-openai-api-key")

# 全局配置实例
autogen_config = AutoGenConfig.from_env()

def setup_autogen_logging():
    """
    配置AutoGen库的日志输出
    根据配置决定是否抑制AutoGen的详细日志
    """
    import logging
    
    # 获取配置
    config = autogen_config
    
    # AutoGen相关的日志记录器名称
    autogen_loggers = [
        "autogen_core",              # AutoGen核心日志
        "autogen_core.events",       # AutoGen事件日志  
        "autogen_agentchat",         # AutoGen聊天日志
        "autogen_agentchat.agents",  # AutoGen智能体日志
        "autogen_agentchat.teams",   # AutoGen团队日志
        "autogen_ext",               # AutoGen扩展日志
        "autogen_ext.models",        # AutoGen模型日志
        "autogen",                   # AutoGen根命名空间
    ]
    
    if config.suppress_autogen_logs:
        # 完全抑制AutoGen日志 - 只显示ERROR级别
        target_level = logging.ERROR
        print("🔇 已禁用AutoGen详细日志输出，只显示错误信息")
    elif config.show_autogen_errors_only:
        # 只显示WARNING和ERROR
        target_level = logging.WARNING
        print("⚠️ AutoGen日志已设置为WARNING级别，减少详细输出")
    else:
        # 使用配置的日志级别
        target_level = getattr(logging, config.autogen_log_level.upper(), logging.WARNING)
        print(f"📝 AutoGen日志级别设置为: {config.autogen_log_level}")
    
    # 应用日志级别到所有AutoGen日志记录器
    for logger_name in autogen_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(target_level)
        
        # 如果完全抑制，还可以禁用传播
        if config.suppress_autogen_logs:
            logger.propagate = False
    
    print(f"✅ AutoGen日志配置完成，共配置了{len(autogen_loggers)}个日志记录器")

# 预定义的智能体角色配置
AGENT_ROLES = {
    "analyst": {
        "name": "分析师",
        "icon": "🔍",
        "system_message": """你是一个专业的分析师。
你的职责是：
1. 深入分析问题的本质和根本原因
2. 识别关键因素和影响要素
3. 提供客观、准确的分析结果
4. 为后续决策提供数据支持

请保持分析的逻辑性和系统性。"""
    },
    
    "planner": {
        "name": "规划师",
        "icon": "📋",
        "system_message": """你是一个任务规划专家。
你的职责是：
1. 制定详细的执行计划
2. 分解复杂任务为可执行步骤
3. 合理安排任务优先级和时间
4. 协调各方资源和人员

请确保计划的可行性和完整性。"""
    },
    
    "executor": {
        "name": "执行者",
        "icon": "⚡",
        "system_message": """你是一个执行专家。
你的职责是：
1. 按照计划执行具体任务
2. 解决执行过程中的技术问题
3. 提供实用的解决方案
4. 确保任务按时完成

请注重实践性和可操作性。"""
    },
    
    "reviewer": {
        "name": "审核者",
        "icon": "✅",
        "system_message": """你是一个质量审核专家。
你的职责是：
1. 审核工作成果的质量
2. 检查是否满足要求
3. 提出改进建议
4. 确保最终交付标准

当审核通过时，请明确说明"审核通过，任务完成"。"""
    },
    
    "researcher": {
        "name": "研究员",
        "icon": "🔬",
        "system_message": """你是一个信息研究专家。
你的职责是：
1. 收集和整理相关信息
2. 进行深入的调研分析
3. 验证信息的准确性
4. 提供有价值的研究结果

请确保研究的深度和广度。"""
    },
    
    "solver": {
        "name": "解决方案专家",
        "icon": "💡",
        "system_message": """你是一个解决方案专家。
你的职责是：
1. 基于分析结果提供创新解决方案
2. 考虑方案的可行性和成本效益
3. 提供详细的实施指导
4. 预测和应对潜在风险

当你完成解决方案时，请说"解决方案已完成"。"""
    }
}

# 常用的终止条件关键词
TERMINATION_KEYWORDS = [
    "任务完成",
    "工作完成", 
    "审核通过，任务完成",
    "解决方案已完成",
    "问题已解决",
    "执行完毕",
    "目标达成"
] 