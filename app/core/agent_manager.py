import os
import importlib
import inspect
import uuid
import asyncio
import logging
from typing import Dict, List, Optional, Any, AsyncGenerator, Type, Union
from datetime import datetime

from app.core.base_agent import BaseReactAgent, BaseAgent, BaseAutoGenAgent

# 配置日志记录器
logger = logging.getLogger(__name__)

class AgentSession:
    """智能体会话类，用于管理单个会话的状态"""
    
    def __init__(self, session_id: str, agent_instance: Union[BaseReactAgent, BaseAutoGenAgent], user_id: str = None):
        self.session_id = session_id
        self.agent_instance = agent_instance
        self.user_id = user_id  # 关联用户ID
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.conversation_count = 0
        self.is_active = False
        self.is_stopped = False  # 停止标记
        self.stop_reason = None  # 停止原因
        self.task_cancelled = False  # 任务取消标记，用于中断异步任务
        
        logger.debug(f"🆕 创建新会话: {session_id} (用户: {user_id}, 智能体: {agent_instance.agent_id})")
    
    def update_activity(self):
        """更新会话活动时间"""
        self.last_activity = datetime.now()
        self.conversation_count += 1
        logger.debug(f"🔄 会话 {self.session_id} 活动更新: 对话次数 {self.conversation_count}")
    
    def stop(self, reason: str = "用户主动停止"):
        """停止会话"""
        self.is_stopped = True
        self.stop_reason = reason
        self.is_active = False
        self.task_cancelled = True  # 设置任务取消标记
        logger.info(f"⏹️ 会话 {self.session_id} 已停止: {reason}")
    
    def resume(self):
        """恢复会话"""
        self.is_stopped = False
        self.stop_reason = None
        self.task_cancelled = False
        # 恢复智能体实例状态
        self.agent_instance.resume()
        logger.info(f"▶️ 会话 {self.session_id} 已恢复")


class AgentManager:
    """增强版智能体管理器，支持ReAct循环和SSE流式输出"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        """单例模式，确保全局只有一个AgentManager实例"""
        if cls._instance is None:
            cls._instance = super(AgentManager, cls).__new__(cls)
            logger.info("🏗️ 创建 AgentManager 单例实例")
        return cls._instance
    
    def __init__(self): 
        """初始化AgentManager实例"""
        if not self._initialized:
            logger.info("🚀 初始化 AgentManager")
            self.agent_classes: Dict[str, Type[Union[BaseReactAgent, BaseAutoGenAgent]]] = {}
            self.sessions: Dict[str, AgentSession] = {}
            self._load_agents()
            AgentManager._initialized = True
            logger.info(f"✅ AgentManager 初始化完成，加载了 {len(self.agent_classes)} 个智能体")
        
    def _load_agents(self):
        """加载agents目录下的所有智能体"""
        agents_dir = "app/agents"
        logger.info(f"📂 开始加载智能体目录: {agents_dir}")
        
        if not os.path.exists(agents_dir):
            logger.error(f"❌ 智能体目录 {agents_dir} 不存在")
            return
        
        loaded_count = 0
        failed_count = 0
        
        for filename in os.listdir(agents_dir):
            if filename.endswith(".py") and not filename.startswith("__"):
                module_name = filename[:-3]
                module_path = f"app.agents.{module_name}"
                
                try:
                    logger.debug(f"🔍 尝试加载模块: {module_path}")
                    module = importlib.import_module(module_path)
                    
                    # 查找模块中继承自BaseReactAgent、BaseAgent或BaseAutoGenAgent的类
                    for name, obj in inspect.getmembers(module):
                        if (inspect.isclass(obj) and 
                            issubclass(obj, (BaseReactAgent, BaseAgent, BaseAutoGenAgent)) and 
                            obj not in (BaseReactAgent, BaseAgent, BaseAutoGenAgent) and
                            hasattr(obj, "agent_id")):
                            
                            agent_id = getattr(obj, "agent_id")
                            self.agent_classes[agent_id] = obj
                            loaded_count += 1
                            logger.info(f"✅ 成功加载智能体: {agent_id} ({obj.name if hasattr(obj, 'name') else agent_id})")
                            
                except Exception as e:
                    failed_count += 1
                    logger.error(f"❌ 加载智能体 {module_name} 时出错: {str(e)}")
        
        logger.info(f"📊 智能体加载完成: 成功 {loaded_count} 个, 失败 {failed_count} 个")
    
    def _get_agent_type(self, agent_class) -> str:
        """获取智能体类型"""
        # 检查是否为AutoGen v0.6智能体
        if issubclass(agent_class, BaseAutoGenAgent):
            return "autogen"
        
        # 检查是否为ReAct智能体
        if issubclass(agent_class, BaseReactAgent):
            return "react"
        
        # 检查是否为AutoGen Legacy智能体（0.9.0版本）
        if issubclass(agent_class, BaseAgent):
            # 检查类名或方法特征
            if hasattr(agent_class, '_create_agents') or 'AutoGen' in agent_class.__name__:
                return "autogen_legacy"
        
        return "legacy"
    
    def list_agents(self) -> List[Dict[str, Any]]:
        """列出所有可用的智能体"""
        agents = [
            {
                "id": agent_id,
                "name": getattr(agent_class, "name", agent_id),
                "description": getattr(agent_class, "description", ""),
                "icon": getattr(agent_class, "icon", "🤖"),
                "type": self._get_agent_type(agent_class)
            }
            for agent_id, agent_class in self.agent_classes.items()
        ]
        logger.debug(f"📋 返回 {len(agents)} 个可用智能体")
        return agents
    
    def get_agent_info(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """获取指定智能体的详细信息"""
        logger.debug(f"🔍 获取智能体信息: {agent_id}")
        
        if agent_id not in self.agent_classes:
            logger.warning(f"⚠️ 智能体 {agent_id} 不存在")
            return None
        
        agent_class = self.agent_classes[agent_id]
        
        # 创建临时实例以获取工具信息
        try:
            logger.debug(f"🔧 创建智能体 {agent_id} 的临时实例以获取信息")
            temp_instance = agent_class()
            tools_info = []
            
            if hasattr(temp_instance, 'available_tools'):
                tools_info = [
                    tool.to_dict() for tool in temp_instance.available_tools.values()
                ]
                logger.debug(f"🔨 智能体 {agent_id} 有 {len(tools_info)} 个工具")
            
            return {
                "id": agent_id,
                "name": getattr(agent_class, "name", agent_id),
                "description": getattr(agent_class, "description", ""),
                "icon": getattr(agent_class, "icon", "🤖"),
                "type": self._get_agent_type(agent_class),
                "tools": tools_info,
                "capabilities": self._get_agent_capabilities(agent_class)
            }
        except Exception as e:
            logger.error(f"❌ 获取智能体 {agent_id} 信息时出错: {str(e)}")
            return {
                "id": agent_id,
                "name": getattr(agent_class, "name", agent_id),
                "description": getattr(agent_class, "description", ""),
                "icon": getattr(agent_class, "icon", "🤖"),
                "type": self._get_agent_type(agent_class),
                "tools": [],
                "capabilities": [],
                "error": f"获取智能体信息时出错: {str(e)}"
            }
    
    def _get_agent_capabilities(self, agent_class) -> List[str]:
        """获取智能体的能力列表"""
        capabilities = []
        
        # 基础能力
        capabilities.append("对话交互")
        
        agent_type = self._get_agent_type(agent_class)
        
        if agent_type == "autogen":
            capabilities.extend([
                "多智能体协作",
                "AutoGen 6工作流",
                "流式输出",
                "智能体间通信"
            ])
        elif agent_type == "autogen_legacy":
            capabilities.extend([
                "多智能体协作",
                "AutoGen传统工作流",
                "群聊管理",
                "智能体轮转"
            ])
        elif agent_type == "react":
            capabilities.extend([
                "ReAct循环推理",
                "动态工具选择",
                "流式输出",
                "多轮对话"
            ])
        
        # 如果智能体有自定义能力，添加它们
        if hasattr(agent_class, 'capabilities'):
            capabilities.extend(getattr(agent_class, 'capabilities', []))
        
        return capabilities
        
    async def run_agent(
        self,
        agent_id: str,
        query: str,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        stream: bool = True
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        运行指定的智能体，支持流式输出
        
        Args:
            agent_id: 智能体ID
            query: 用户查询
            session_id: 会话ID，用于保持会话状态
            user_id: 用户ID，用于会话隔离
            context: 上下文信息
            stream: 是否使用流式输出
            
        Yields:
            智能体执行过程中产生的结果
        """
        logger.info(f"🚀 开始运行智能体: {agent_id}")
        logger.info(f"📋 运行参数: 用户查询='{query}', 会话ID={session_id}, 用户ID={user_id}")
        
        # 验证智能体是否存在
        if agent_id not in self.agent_classes:
            error_msg = f"未找到ID为 {agent_id} 的智能体"
            logger.error(f"❌ {error_msg}")
            yield {
                "type": "error", 
                "message": error_msg,
                "available_agents": list(self.agent_classes.keys())
            }
            return
        
        # 创建或获取会话
        logger.info(f"🔄 获取或创建会话")
        session = await self._get_or_create_session(agent_id, session_id, user_id)
        logger.info(f"✅ 使用会话: {session.session_id}")
        
        # 更新会话活动
        session.update_activity()
        session.is_active = True
        logger.info(f"📊 会话状态: 活跃=True, 对话次数={session.conversation_count}")
        
        # 发送会话信息
        session_info = {
            "type": "session_info", 
            "session_id": session.session_id,
            "agent_id": agent_id,
            "conversation_count": session.conversation_count
        }
        logger.debug(f"📡 发送会话信息: {session_info}")
        yield session_info
        
        # 发送开始消息
        agent_name = getattr(self.agent_classes[agent_id], "name", agent_id)
        start_info = {
            "type": "agent_start", 
            "agent_id": agent_id, 
            "agent_name": agent_name,
            "query": query
        }
        logger.info(f"🎬 智能体 '{agent_name}' 开始处理查询")
        yield start_info
        
        try:
            # 运行智能体
            logger.info(f"⚡ 启动智能体实例运行")
            agent_generator = session.agent_instance.run(query, context or {})
            
            result_count = 0
            async for result in agent_generator:
                result_count += 1
                result_type = result.get("type", "unknown")
                
                # 检查会话是否被停止
                if session.is_stopped or session.task_cancelled:
                    logger.warning(f"⏹️ 检测到会话被停止: {session.stop_reason}")
                    # 通知智能体实例取消任务
                    session.agent_instance.cancel(session.stop_reason or "会话被停止")
                    yield {
                        "type": "stopped",
                        "message": f"会话已被停止: {session.stop_reason}",
                        "session_id": session.session_id,
                        "agent_id": agent_id,
                        "timestamp": datetime.now().isoformat()
                    }
                    break
                
                # 记录不同类型的结果
                if result_type == "thinking":
                    logger.debug(f"智能体思考: {result.get('message', '')[:50]}...")
                elif result_type == "decision":
                    logger.info(f"智能体决策: {result.get('content', '')[:100]}...")
                elif result_type == "tool_start":
                    logger.info(f"开始执行功能: {result.get('function_name')} - {result.get('parameters')}")
                elif result_type == "tool_result":
                    logger.info(f"功能执行完成: {result.get('function_name')}")
                    logger.debug(f"📄 功能结果: {str(result.get('result', ''))[:150]}...")
                elif result_type == "error":
                    logger.error(f"智能体执行错误: {result.get('message')}")
                elif result_type == "summary":
                    logger.info(f"生成摘要")
                elif result_type == "status":
                    logger.info(f"状态: {result.get('message')}")
                
                # 为每个结果添加会话和智能体信息
                result["session_id"] = session.session_id
                result["agent_id"] = agent_id
                result["user_id"] = session.user_id
                result["timestamp"] = datetime.now().isoformat()
                
                yield result
                
                # 检查是否需要中断
                if result.get("type") == "error":
                    logger.error(f"💥 因错误中断执行: {result.get('message')}")
                    break
            
            logger.info(f"🏁 智能体执行完成，共返回 {result_count} 个结果")
                    
        except Exception as e:
            logger.error(f"💥 智能体运行过程中发生异常: {str(e)}", exc_info=True)
            error_result = {
                "type": "error", 
                "message": f"智能体运行错误: {str(e)}",
                "session_id": session.session_id,
                "agent_id": agent_id,
                "timestamp": datetime.now().isoformat()
            }
            yield error_result
        finally:
            # 标记会话为非活跃状态
            session.is_active = False
            logger.info(f"🔄 会话 {session.session_id} 标记为非活跃状态")
    
    async def _get_or_create_session(self, agent_id: str, session_id: Optional[str] = None, user_id: Optional[str] = None) -> AgentSession:
        """获取或创建会话"""
        if session_id and session_id in self.sessions:
            # 验证会话的智能体类型和用户是否匹配
            existing_session = self.sessions[session_id]
            if (existing_session.agent_instance.__class__ == self.agent_classes[agent_id] and 
                existing_session.user_id == user_id):
                # 恢复会话状态（如果之前被停止）
                existing_session.resume()
                
                # 对于AutoGen智能体，重置内部状态以确保会话隔离
                if isinstance(existing_session.agent_instance, BaseAutoGenAgent):
                    logger.info(f"🔄 检测到AutoGen智能体，重置内部状态确保会话隔离")
                    if hasattr(existing_session.agent_instance, 'reset_state'):
                        existing_session.agent_instance.reset_state()
                    elif hasattr(existing_session.agent_instance, '_reset_agent_state'):
                        existing_session.agent_instance._reset_agent_state()
                
                logger.info(f"♻️ 复用现有会话: {session_id}")
                return existing_session
            else:
                # 智能体类型或用户不匹配，创建新会话
                logger.warning(f"⚠️ 会话 {session_id} 的智能体类型或用户不匹配，创建新会话")
        
        # 创建新会话
        if not session_id:
            session_id = str(uuid.uuid4())
            logger.info(f"🆔 生成新会话ID: {session_id}")
        
        logger.info(f"🏭 创建智能体 {agent_id} 的实例")
        agent_class = self.agent_classes[agent_id]
        try:
            agent_instance = agent_class()
            logger.info(f"✅ 智能体实例创建成功")
        except Exception as e:
            logger.error(f"❌ 创建智能体实例失败: {str(e)}")
            raise
        
        session = AgentSession(session_id, agent_instance, user_id)
        self.sessions[session_id] = session
        
        logger.info(f"✅ 新会话创建完成: {session_id}")
        return session
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话信息"""
        if session_id not in self.sessions:
            return None
        
        session = self.sessions[session_id]
        
        return {
            "session_id": session.session_id,
            "agent_id": session.agent_instance.agent_id,
            "agent_name": getattr(session.agent_instance.__class__, "name", "未知智能体"),
            "user_id": session.user_id,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "conversation_count": session.conversation_count,
            "is_active": session.is_active,
            "is_stopped": session.is_stopped,
            "stop_reason": session.stop_reason
        }
    
    def list_sessions(self) -> List[Dict[str, Any]]:
        """列出所有会话"""
        return [
            self.get_session_info(session_id) 
            for session_id in self.sessions.keys()
        ]
    
    def cleanup_sessions(self, max_inactive_minutes: int = 30):
        """清理非活跃会话"""
        current_time = datetime.now()
        sessions_to_remove = []
        
        for session_id, session in self.sessions.items():
            if not session.is_active:
                inactive_time = (current_time - session.last_activity).total_seconds() / 60
                if inactive_time > max_inactive_minutes:
                    sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            del self.sessions[session_id]
            print(f"已清理非活跃会话: {session_id}")
        
        return len(sessions_to_remove)
    
    def terminate_session(self, session_id: str) -> bool:
        """终止指定会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            return True
        return False
    
    def stop_session(self, session_id: str, user_id: Optional[str] = None, reason: str = "用户主动停止") -> Dict[str, Any]:
        """停止指定会话的智能体运行"""
        if session_id not in self.sessions:
            return {
                "success": False,
                "message": f"会话 {session_id} 不存在"
            }
        
        session = self.sessions[session_id]
        
        # 验证用户权限
        if user_id and session.user_id != user_id:
            return {
                "success": False,
                "message": "无权限停止此会话"
            }
        
        session.stop(reason)
        
        # 通知智能体实例取消任务
        session.agent_instance.cancel(reason)
        
        return {
            "success": True,
            "message": f"会话 {session_id} 已停止",
            "session_id": session_id,
            "reason": reason
        }
    
    def resume_session(self, session_id: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """恢复指定会话"""
        if session_id not in self.sessions:
            return {
                "success": False,
                "message": f"会话 {session_id} 不存在"
            }
        
        session = self.sessions[session_id]
        
        # 验证用户权限
        if user_id and session.user_id != user_id:
            return {
                "success": False,
                "message": "无权限恢复此会话"
            }
        
        session.resume()
        
        # 恢复智能体实例状态
        session.agent_instance.resume()
        
        return {
            "success": True,
            "message": f"会话 {session_id} 已恢复",
            "session_id": session_id
        }
    
    async def switch_agent(
        self, 
        session_id: str, 
        new_agent_id: str, 
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """在现有会话中切换智能体"""
        if session_id not in self.sessions:
            return {
                "success": False,
                "message": f"会话 {session_id} 不存在"
            }
        
        if new_agent_id not in self.agent_classes:
            return {
                "success": False,
                "message": f"智能体 {new_agent_id} 不存在",
                "available_agents": list(self.agent_classes.keys())
            }
        
        session = self.sessions[session_id]
        
        # 验证用户权限
        if user_id and session.user_id != user_id:
            return {
                "success": False,
                "message": "无权限切换此会话的智能体"
            }
        
        # 停止当前智能体
        old_agent_id = session.agent_instance.agent_id
        session.stop("切换智能体")
        session.agent_instance.cancel("切换智能体")
        
        # 创建新的智能体实例
        new_agent_class = self.agent_classes[new_agent_id]
        new_agent_instance = new_agent_class()
        
        # 更新会话
        session.agent_instance = new_agent_instance
        session.resume()
        session.update_activity()
        
        return {
            "success": True,
            "message": f"会话 {session_id} 已从 {old_agent_id} 切换到 {new_agent_id}",
            "session_id": session_id,
            "old_agent_id": old_agent_id,
            "new_agent_id": new_agent_id,
            "new_agent_name": getattr(new_agent_class, "name", new_agent_id)
        }
    
    def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """获取指定用户的所有会话"""
        user_sessions = []
        for session_id, session in self.sessions.items():
            if session.user_id == user_id:
                user_sessions.append(self.get_session_info(session_id))
        return user_sessions
    
    async def run_agent_stream_sse(
        self,
        agent_id: str,
        query: str,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[str, None]:
        """
        运行智能体并返回SSE格式的流式数据
        
        Args:
            agent_id: 智能体ID
            query: 用户查询
            session_id: 会话ID
            user_id: 用户ID
            context: 上下文信息
            
        Yields:
            SSE格式的数据流
        """
        import json
        
        try:
            async for result in self.run_agent(agent_id, query, session_id, user_id, context):
                # 将结果转换为SSE格式
                if isinstance(result, dict):
                    event_data = json.dumps(result, ensure_ascii=False)
                else:
                    event_data = str(result)
                
                yield f"data: {event_data}\n\n"
            
            # 发送完成事件
            yield f"data: {json.dumps({'type': 'complete'})}\n\n"
            
        except Exception as e:
            # 发送错误事件
            error_data = json.dumps({
                "type": "error", 
                "message": f"SSE流处理错误: {str(e)}",
                "timestamp": datetime.now().isoformat()
            })
            yield f"data: {error_data}\n\n"
        
        # 结束流
        # yield "event: close\ndata: 'close'\n\n"
    
    def reload_agents(self):
        """重新加载智能体"""
        print("正在重新加载智能体...")
        
        # 清除当前加载的智能体
        self.agent_classes.clear()
        
        # 重新加载
        self._load_agents()
        
        print(f"重新加载完成，共加载 {len(self.agent_classes)} 个智能体")
        
        return len(self.agent_classes)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取管理器统计信息"""
        active_sessions = sum(1 for session in self.sessions.values() if session.is_active)
        total_conversations = sum(session.conversation_count for session in self.sessions.values())
        
        return {
            "total_agents": len(self.agent_classes),
            "total_sessions": len(self.sessions),
            "active_sessions": active_sessions,
            "total_conversations": total_conversations,
            "agent_types": {
                "react_agents": sum(1 for cls in self.agent_classes.values() if issubclass(cls, BaseReactAgent)),
                "legacy_agents": sum(1 for cls in self.agent_classes.values() if not issubclass(cls, BaseReactAgent))
            }
        } 