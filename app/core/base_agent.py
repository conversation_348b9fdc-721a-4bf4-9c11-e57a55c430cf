from typing import Dict, Any, List, AsyncGenerator, Optional, Callable, Union
import asyncio
import json
from abc import ABC, abstractmethod
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.messages import HumanMessage, AIMessage
from datetime import datetime

from app.core.llm import get_chat_model
from typing import TYPE_CHECKING
import logging

# 延迟导入 AutoGen 相关模块，避免循环导入
if TYPE_CHECKING:
    from autogen_agentchat.agents import AssistantAgent
    from autogen_agentchat.messages import TextMessage
    from autogen_core.base import MessageContext

logger = logging.getLogger(__name__)

class StreamingTool:
    """流式工具基类"""
    
    def __init__(self, name: str, description: str, func: Callable, parameters: List[str]):
        self.name = name
        self.description = description
        self.func = func
        self.parameters = parameters
    
    async def execute(self, *args, **kwargs):
        """执行工具函数"""
        if asyncio.iscoroutinefunction(self.func):
            return await self.func(*args, **kwargs)
        else:
            return self.func(*args, **kwargs)
    
    def to_dict(self):
        """转换为字典格式，用于工具选择"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters
        }


class BaseReactAgent(ABC):
    """基础ReAct智能体类，所有智能体都应该继承这个类"""
    
    # 智能体元信息，需要子类定义
    agent_id: str = "base_react_agent"
    name: str = "基础ReAct智能体"
    description: str = "基础ReAct智能体实现"
    icon: str = ""
    
    def __init__(self):
        # 初始化LLM
        self.llm, self.streaming_handler = get_chat_model(streaming=True, temperature=0)
        
        # 工具管理
        self.available_tools: Dict[str, StreamingTool] = {}
        self.tool_results: Dict[str, Any] = {}
        
        # 对话历史
        self.conversation_history: List[Union[HumanMessage, AIMessage]] = []
        
        # 停止控制
        self.is_cancelled = False
        self.cancel_reason = None
        
        # 初始化工具
        self._register_tools()
    
    @abstractmethod
    def _register_tools(self):
        """注册工具，需要子类实现"""
        pass
    
    def cancel(self, reason: str = "任务被取消"):
        """取消当前执行的任务"""
        self.is_cancelled = True
        self.cancel_reason = reason
    
    def resume(self):
        """恢复任务执行"""
        self.is_cancelled = False
        self.cancel_reason = None
    
    def add_tool(self, name: str, function_name: str, description: str, func: Callable, parameters: List[str]):
        """添加工具到智能体"""
        tool = StreamingTool(name, description, func, parameters)
        tool.function_name = function_name  # 添加中文名称
        self.available_tools[name] = tool
    
    def get_tools_description(self) -> str:
        """获取所有工具的描述"""
        descriptions = []
        for name, tool in self.available_tools.items():
            function_name = getattr(tool, 'function_name', name)
            descriptions.append(f"- {name} ({function_name}): {tool.description}")
        return "\n".join(descriptions)
    
    async def run(self, query: str, context: Dict[str, Any] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """
        运行智能体的增强版ReAct循环
        
        Args:
            query: 用户查询
            context: 上下文信息
            
        Yields:
            智能体执行过程中产生的结果
        """
        # 重置取消状态 
        self.resume()
        
        yield {"type": "status", "message": f"开始处理任务: {query}"}
        
        # 初始化对话历史
        self.conversation_history = [HumanMessage(content=f"任务: {query}")]
        
        # 清空之前的工具结果
        self.tool_results = {}
        
        # 开始ReAct循环
        async for result in self._react_loop(query, context or {}):
            if self.is_cancelled:
                yield {
                    "type": "stopped",
                    "message": f"任务已被取消: {self.cancel_reason}",
                    "timestamp": datetime.now().isoformat()
                }
                break
            yield result
    
    async def _react_loop(self, user_input: str, context: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """
        增强版ReAct循环实现
        """
        max_iterations = 10  # 防止无限循环
        iteration = 0
        
        while iteration < max_iterations:
            # 检查是否被取消
            if self.is_cancelled:
                yield {
                    "type": "stopped", 
                    "message": f"任务已被取消: {self.cancel_reason}",
                    "timestamp": datetime.now().isoformat()
                }
                break
                
            iteration += 1
            
            # 构建工具选择提示
            decision_prompt = self._get_decision_prompt()
            
            # 构建执行历史
            execution_history = self._build_execution_history()
            
            # 决策参数
            decision_params = {
                "task": user_input,
                "execution_history": execution_history,
                "tools": self.get_tools_description(),
                "context": json.dumps(context, ensure_ascii=False)
            }
            
            # 构建决策链
            decision_chain = decision_prompt | self.llm | StrOutputParser()
            
            yield {"type": "thinking", "message": "智能体思考中，正在决定下一步... "}
            
            try:
                # 检查是否被取消
                if self.is_cancelled:
                    break
                    
                # 获取决策
                decision = await decision_chain.ainvoke(decision_params)
                
                # 再次检查是否被取消
                if self.is_cancelled:
                    break
                    
                yield {"type": "decision", "content": decision}
                
                # 解析决策
                parsed_decision = self._parse_decision(decision)
                
                if not parsed_decision:
                    yield {"type": "error", "message": "决策解析失败"}
                    break
                
                # 检查是否应该结束任务
                if parsed_decision.get("action") == "任务完成":
                    yield {"type": "status", "message": f"任务完成，原因: {parsed_decision.get('reason', '未知')}"}
                    break
                
                # 检查是否符合任务需求
                if self._should_complete_task(user_input, parsed_decision):
                    yield {"type": "status", "message": "根据任务需求，当前阶段已完成"}
                    break
                
                # 执行选中的工具
                tool_name = parsed_decision.get("tool_name")
                function_name = parsed_decision.get("function_name")
                tool_param = parsed_decision.get("tool_param")
                
                if tool_name in self.available_tools:
                    yield {"type": "tool_start", "tool_name": tool_name, "function_name": function_name, "parameters": tool_param}
                    
                    try:
                        # 检查是否被取消
                        if self.is_cancelled:
                            break
                            
                        tool = self.available_tools[tool_name]
                        
                        # 如果工具是流式的，则逐步输出结果
                        if asyncio.iscoroutinefunction(tool.func):
                            # 检查是否是生成器函数
                            result = await tool.execute(tool_param)
                        else:
                            result = tool.execute(tool_param)
                        
                        # 检查是否被取消
                        if self.is_cancelled:
                            break
                        
                        # 存储结果
                        self.tool_results[tool_name] = result
                        
                        yield {"type": "tool_result", "tool_name": tool_name, "result": result}
                        
                        # 更新对话历史
                        self.conversation_history.append(
                            AIMessage(content=f"已执行工具: {tool_name}, 结果已存储")
                        )
                        
                    except Exception as tool_error:
                        error_msg = f"工具执行错误: {str(tool_error)}"
                        yield {"type": "error", "message": error_msg}
                        break
                else:
                    error_msg = f"未知工具: {tool_name}"
                    yield {"type": "error", "message": error_msg}
                    break
                    
            except Exception as e:
                error_msg = f"决策过程错误: {str(e)}"
                yield {"type": "error", "message": error_msg}
                break
        
        # 检查是否被取消
        if self.is_cancelled:
            return
            
        # 生成最终摘要
        yield {"type": "status", "message": "[生成任务摘要]"}
        async for summary_chunk in self._generate_summary(user_input):
            if self.is_cancelled:
                break
            yield summary_chunk
        
        yield {"type": "status", "message": "任务流程全部结束"}
    
    def _get_decision_prompt(self) -> ChatPromptTemplate:
        """获取决策提示模板，可以被子类重写以自定义决策逻辑"""
        return ChatPromptTemplate.from_template(
            """
            你是一个智能助手，帮助用户完成任务。根据当前任务状态，请决定下一步应该使用哪个工具。
            
            当前任务: {task}
            上下文信息: {context}
            
            已执行的工具和结果:
            {execution_history}
            
            可用工具:
            {tools}
            
            请仔细分析当前任务的具体需求，并决定下一步操作：
            1. 分析任务是否需要更多信息或处理
            2. 根据用户需求决定是否继续执行工具
            3. 不要执行用户未明确要求的操作
            
            请回答必须包括：
            1. 选择的工具名称（必须是可用工具列表中的一个）或"任务完成"
            2. 工具的中文名称（如果选择了工具）
            3. 使用该工具的输入参数
            4. 简要说明选择此工具或结束任务的原因
            
            格式:
            工具: <工具名称或"任务完成">
            功能名: <工具中文名称>
            参数: <工具输入参数>
            原因: <简要说明选择理由>
            """
        )
    
    def _parse_decision(self, decision: str) -> Optional[Dict[str, Any]]:
        """解析决策文本"""
        try:
            decision_lines = decision.strip().split("\n")
            tool_line = next((line for line in decision_lines if line.startswith("工具:")), "")
            function_line = next((line for line in decision_lines if line.startswith("功能名:")), "")
            param_line = next((line for line in decision_lines if line.startswith("参数:")), "")
            reason_line = next((line for line in decision_lines if line.startswith("原因:")), "")
            
            tool_name = tool_line.replace("工具:", "").strip()
            function_name = function_line.replace("功能名:", "").strip() if function_line else ""
            tool_param = param_line.replace("参数:", "").strip()
            reason = reason_line.replace("原因:", "").strip()
            
            # 如果没有提供中文功能名，则尝试从工具中获取
            if not function_name and tool_name in self.available_tools:
                function_name = getattr(self.available_tools[tool_name], 'function_name', tool_name)
            
            return {
                "action": tool_name,
                "tool_name": tool_name,
                "function_name": function_name,
                "tool_param": tool_param,
                "reason": reason
            }
        except Exception as e:
            return None
    
    def _should_complete_task(self, user_input: str, decision: Dict[str, Any]) -> bool:
        """判断是否应该完成任务，可以被子类重写以实现自定义逻辑"""
        # 基础实现：检查任务类型和已执行的工具
        return False
    
    def _build_execution_history(self) -> str:
        """构建执行历史"""
        if not self.tool_results:
            return "暂无执行历史"
        
        history_parts = []
        for tool_name, result in self.tool_results.items():
            result_summary = str(result)[:100] + "..." if len(str(result)) > 100 else str(result)
            history_parts.append(f"- 已执行 {tool_name}，结果概要：{result_summary}")
        
        return "\n".join(history_parts)
    
    async def _generate_summary(self, user_input: str) -> AsyncGenerator[Dict[str, Any], None]:
        """生成最终摘要"""
        summary_prompt = ChatPromptTemplate.from_template(
            """
            请根据以下执行结果，生成一份简洁的总结报告：
            
            用户任务: {task}
            
            执行结果:
            {execution_results}
            
            请保持简洁明了，突出重点内容。
            """
        )
        
        # 构建所有工具的执行结果
        execution_results = "\n\n".join([
            f"{name} 结果\n{result}" 
            for name, result in self.tool_results.items()
        ])
        
        summary_chain = summary_prompt | self.llm | StrOutputParser()
        
        # 流式输出摘要
        async for chunk in summary_chain.astream({
            "execution_results": execution_results, 
            "task": user_input
        }):
            yield {"type": "summary", "content": chunk}

    def _get_executed_tools_by_category(self, tool_categories: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """
        辅助方法：根据工具分类获取已执行的工具
        
        Args:
            tool_categories: 工具分类字典，格式为 {"分类名": ["工具1", "工具2"]}
        
        Returns:
            字典，格式为 {"分类名": ["已执行的工具1", "已执行的工具2"]}
        """
        executed_by_category = {}
        for category, tools in tool_categories.items():
            executed_tools = [tool for tool in tools if tool in self.tool_results]
            executed_by_category[category] = executed_tools
        return executed_by_category

    def _get_tool_execution_summary(self, tool_categories: Dict[str, List[str]]) -> str:
        """
        辅助方法：生成工具执行情况的文本摘要
        
        Args:
            tool_categories: 工具分类字典
            
        Returns:
            执行情况的文本描述
        """
        executed_by_category = self._get_executed_tools_by_category(tool_categories)
        summary_lines = []
        
        for category, all_tools in tool_categories.items():
            executed_tools = executed_by_category[category]
            execution_rate = f"{len(executed_tools)}/{len(all_tools)}"
            summary_lines.append(f"{category}: {execution_rate} 个工具已执行")
            
        return "\n".join(summary_lines)

    def _format_tool_result_preview(self, result: str, max_length: int = 200) -> str:
        """
        辅助方法：格式化工具结果预览
        
        Args:
            result: 工具执行结果
            max_length: 最大长度
            
        Returns:
            格式化后的预览文本
        """
        if len(str(result)) > max_length:
            return str(result)[:max_length] + "..."
        return str(result)


# 保持兼容性的旧版BaseAgent类
class BaseAgent(BaseReactAgent):
    """兼容性类，继承新的BaseReactAgent"""
    pass 

class BaseAutoGenAgent(ABC):
    """基础AutoGen智能体类，用于集成AutoGen框架"""
    
    # 智能体元信息，需要子类定义
    agent_id: str = "base_autogen_agent"
    name: str = "基础AutoGen智能体"
    description: str = "基础AutoGen智能体实现"
    icon: str = "🤖"
    
    def __init__(self):
        # 🔥 **优先配置AutoGen日志抑制**
        self._suppress_autogen_logs()
        
        # 初始化基础属性
        self.is_cancelled = False
        self.cancel_reason = None
        self.conversation_history = []
        
        # AutoGen相关属性将在子类中初始化
        self.assistant_agent = None
        self.group_chat = None
        self.group_chat_manager = None
        
        # 初始化AutoGen组件
        self._initialize_autogen()
    
    def _suppress_autogen_logs(self):
        """在智能体初始化时抑制AutoGen日志"""
        try:
            from app.config.autogen_config import setup_autogen_logging
            setup_autogen_logging()
        except Exception as e:
            # 使用基础的日志抑制
            import logging
            autogen_loggers = [
                "autogen_core", "autogen_core.events", "autogen_agentchat",
                "autogen_agentchat.agents", "autogen_agentchat.teams", "autogen_ext"
            ]
            for logger_name in autogen_loggers:
                logging.getLogger(logger_name).setLevel(logging.ERROR)
    
    @abstractmethod
    def _initialize_autogen(self):
        """初始化AutoGen组件，需要子类实现"""
        pass
    
    def cancel(self, reason: str = "任务被取消"):
        """取消当前执行的任务"""
        self.is_cancelled = True
        self.cancel_reason = reason
    
    def resume(self):
        """恢复任务执行"""
        self.is_cancelled = False
        self.cancel_reason = None
    
    def reset_state(self):
        """重置智能体状态 - 基础实现，子类可以重写"""
        logger.info("🔄 重置BaseAutoGenAgent基础状态")
        self.is_cancelled = False
        self.cancel_reason = None
        self.conversation_history.clear()
        
        # 子类应该重写此方法以重置特定状态
    
    async def run(self, query: str, context: Dict[str, Any] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """
        运行AutoGen智能体
        
        Args:
            query: 用户查询
            context: 上下文信息
            
        Yields:
            智能体执行过程中产生的结果
        """
        # 重置取消状态
        self.resume()
        
        yield {"type": "status", "message": f"开始处理任务: {query}"}
        
        try:
            # 执行AutoGen工作流
            async for result in self._run_autogen_workflow(query, context or {}):
                if self.is_cancelled:
                    yield {
                        "type": "stopped",
                        "message": f"任务已被取消: {self.cancel_reason}",
                        "timestamp": datetime.now().isoformat()
                    }
                    break
                yield result
                
        except Exception as e:
            logger.error(f"AutoGen智能体执行出错: {str(e)}")
            yield {
                "type": "error",
                "message": f"智能体执行出错: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    @abstractmethod
    async def _run_autogen_workflow(self, query: str, context: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """
        运行AutoGen工作流，需要子类实现
        
        Args:
            query: 用户查询
            context: 上下文信息
            
        Yields:
            工作流执行过程中的结果
        """
        pass
    
    def get_tools_description(self) -> str:
        """获取工具描述，AutoGen智能体可能不需要显式工具管理"""
        return "AutoGen智能体使用内置的多智能体协作能力"
    
    def _should_complete_task(self, user_input: str, decision: Dict[str, Any]) -> bool:
        """判断任务是否应该完成，可由子类重写"""
        return False 