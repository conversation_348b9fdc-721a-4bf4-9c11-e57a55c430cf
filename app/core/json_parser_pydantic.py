"""
基于PydanticAI的JSON解析器模块
专门用于解决测试用例生成中的JSON解析问题
"""

import json
import re
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field, ValidationError, model_validator
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ==================== Pydantic数据模型 ====================

class TestStep(BaseModel):
    """测试步骤模型"""
    description: str = Field(..., description="步骤描述")
    expected_result: str = Field(..., description="预期结果")
    
    @model_validator(mode='before')
    @classmethod
    def parse_step_format(cls, data):
        """解析 ts:描述 -> ex:结果 格式和新的步骤描述格式"""
        if isinstance(data, dict):
            # 处理标准格式
            for key, value in data.items():
                if key.startswith('ts:') and isinstance(value, str) and value.startswith('ex:'):
                    return {
                        'description': key[3:].strip(),  # 去掉 'ts:' 前缀
                        'expected_result': value[3:].strip()  # 去掉 'ex:' 前缀
                    }
        elif isinstance(data, str):
            # 处理文本格式
            if ' -> ' in data:
                parts = data.split(' -> ', 1)
                return {
                    'description': parts[0].strip(),
                    'expected_result': parts[1].strip()
                }
        
        return data

class TestCase(BaseModel):
    """测试用例模型 - 支持新旧两种格式"""
    name: str = Field(..., description="用例名称")
    case_id: str = Field(..., description="用例编号")
    test_type: str = Field(default="功能测试", description="测试类型") 
    test_level: str = Field(default="系统测试", description="测试级别")
    preconditions: str = Field(default="", description="前置条件")
    steps: List[TestStep] = Field(default_factory=list, description="测试步骤")
    module_name: str = Field(default="", description="所属模块")
    remark: str = Field(default="", description="备注")
    edit_mode: str = Field(default="创建", description="编辑模式")
    tag: str = Field(default="功能测试", description="标签")
    case_level: str = Field(default="P2", description="用例等级")
    case_status: str = Field(default="待执行", description="用例状态")
    
    @model_validator(mode='before')
    @classmethod
    def parse_test_case_format(cls, data):
        """解析测试用例格式 - 支持新旧两种格式"""
        if isinstance(data, dict):
            # 🔥 新格式：直接使用字段名
            if "用例名称" in data and "ID" in data:
                # 新格式解析
                steps = []
                
                # 解析步骤描述和预期结果
                step_descriptions = data.get("步骤描述", "").split('\n') if data.get("步骤描述") else []
                expected_results = data.get("预期结果", "").split('\n') if data.get("预期结果") else []
                
                # 确保步骤和结果数量一致
                max_steps = max(len(step_descriptions), len(expected_results))
                for i in range(max_steps):
                    step_desc = step_descriptions[i].strip() if i < len(step_descriptions) else ""
                    expected_result = expected_results[i].strip() if i < len(expected_results) else ""
                    
                    if step_desc or expected_result:  # 只添加非空步骤
                        steps.append({
                            'description': step_desc,
                            'expected_result': expected_result
                        })
                
                return {
                    'name': data.get("用例名称", ""),
                    'case_id': data.get("ID", ""),
                    'test_type': data.get("标签", "功能测试"),
                    'test_level': data.get("用例等级", "P2"),
                    'preconditions': data.get("前置条件", ""),
                    'steps': steps,
                    'module_name': data.get("所属模块", ""),
                    'remark': data.get("备注", ""),
                    'edit_mode': data.get("编辑模式", "创建"),
                    'tag': data.get("标签", "功能测试"),
                    'case_level': data.get("用例等级", "P2"),
                    'case_status': data.get("用例状态", "待执行")
                }
            
            # 🔥 旧格式：tc: 开头的键
            tc_key = None
            tc_data = None
            
            for key, value in data.items():
                if key.startswith('tc:'):
                    tc_key = key[3:].strip()  # 去掉 'tc:' 前缀
                    tc_data = value
                    break
            
            if tc_key and isinstance(tc_data, dict):
                # 提取基本信息
                result = {
                    'name': tc_key,
                    'case_id': tc_data.get('to', ''),
                    'test_type': tc_data.get('tt', '功能测试'),
                    'test_level': tc_data.get('ti', '系统测试'),
                    'preconditions': tc_data.get('tp', ''),
                    'steps': [],
                    'module_name': '',
                    'remark': '',
                    'edit_mode': '创建',
                    'tag': '功能测试',
                    'case_level': 'P2',
                    'case_status': '待执行'
                }
                
                # 提取测试步骤
                for step_key, step_value in tc_data.items():
                    if step_key.startswith('ts:') and isinstance(step_value, str):
                        step = {
                            'description': step_key[3:].strip(),
                            'expected_result': step_value[3:].strip() if step_value.startswith('ex:') else step_value
                        }
                        result['steps'].append(step)
                
                return result
        
        return data

class TestModule(BaseModel):
    """测试模块模型"""
    name: str = Field(..., description="模块名称")
    test_cases: List[TestCase] = Field(..., description="测试用例列表")
    
    @model_validator(mode='before')
    @classmethod
    def parse_module_format(cls, data):
        """解析模块格式"""
        if isinstance(data, dict):
            # 查找模块名称和用例列表
            for module_name, cases_data in data.items():
                if isinstance(cases_data, list):
                    return {
                        'name': module_name,
                        'test_cases': cases_data
                    }
        return data

class TestSuite(BaseModel):
    """测试套件模型"""
    modules: List[TestModule] = Field(..., description="测试模块列表")
    total_cases: int = Field(0, description="总用例数")
    
    @model_validator(mode='before')
    @classmethod
    def parse_test_suite_format(cls, data):
        """解析测试套件格式"""
        if isinstance(data, dict) and "测试用例" in data:
            modules_data = data["测试用例"]
            if isinstance(modules_data, list):
                return {
                    'modules': modules_data,
                    'total_cases': 0  # 将在后处理中计算
                }
        return data
    
    def model_post_init(self, __context):
        """后处理：计算总用例数"""
        self.total_cases = sum(len(module.test_cases) for module in self.modules)

# ==================== JSON解析器类 ====================

class PydanticJSONParser:
    """基于Pydantic的JSON解析器"""
    
    def __init__(self):
        self.logger = logger
    
    def parse_test_cases_from_text(self, text: str) -> Optional[TestSuite]:
        """从文本中解析测试用例"""
        try:
            # 尝试多种解析策略
            json_data = self._extract_json_from_text(text)
            
            if json_data:
                return self._parse_with_pydantic(json_data)
            else:
                self.logger.warning("未找到有效的JSON数据，尝试文本解析")
                return self._parse_from_plain_text(text)
                
        except Exception as e:
            self.logger.error(f"解析测试用例失败: {e}")
            return None
    
    def _extract_json_from_text(self, text: str) -> Optional[Dict]:
        """从文本中提取JSON数据"""
        strategies = [
            self._extract_json_code_block,
            self._extract_json_brackets,
            self._extract_json_pattern
        ]
        
        for strategy in strategies:
            try:
                result = strategy(text)
                if result:
                    return result
            except Exception as e:
                self.logger.debug(f"JSON提取策略失败: {e}")
        
        return None
    
    def _extract_json_code_block(self, text: str) -> Optional[Dict]:
        """提取JSON代码块"""
        pattern = r'```json\s*(.*?)\s*```'
        matches = re.findall(pattern, text, re.DOTALL | re.IGNORECASE)
        
        for match in matches:
            try:
                return json.loads(match.strip())
            except json.JSONDecodeError:
                continue
        
        return None
    
    def _extract_json_brackets(self, text: str) -> Optional[Dict]:
        """提取大括号内的JSON"""
        # 找到第一个 { 和最后一个 }
        start = text.find('{')
        end = text.rfind('}')
        
        if start != -1 and end != -1 and end > start:
            json_str = text[start:end+1]
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                pass
        
        return None
    
    def _extract_json_pattern(self, text: str) -> Optional[Dict]:
        """使用正则表达式提取JSON模式"""
        # 查找类似 {"测试用例": [...]} 的模式
        pattern = r'\{\s*["\']测试用例["\']\s*:\s*\[.*?\]\s*\}'
        matches = re.findall(pattern, text, re.DOTALL)
        
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue
        
        return None
    
    def _parse_with_pydantic(self, json_data: Dict) -> Optional[TestSuite]:
        """使用Pydantic模型解析JSON数据"""
        try:
            test_suite = TestSuite.model_validate(json_data)
            self.logger.info(f"✅ 成功解析测试套件，包含 {len(test_suite.modules)} 个模块，{test_suite.total_cases} 个用例")
            return test_suite
            
        except ValidationError as e:
            self.logger.error(f"Pydantic验证失败: {e}")
            # 尝试修复常见问题
            return self._try_fix_and_parse(json_data)
    
    def _try_fix_and_parse(self, json_data: Dict) -> Optional[TestSuite]:
        """尝试修复并重新解析JSON数据"""
        try:
            # 常见修复策略
            fixed_data = self._fix_common_issues(json_data)
            test_suite = TestSuite.model_validate(fixed_data)
            self.logger.info("✅ 修复后成功解析测试套件")
            return test_suite
            
        except Exception as e:
            self.logger.error(f"修复后仍无法解析: {e}")
            return None
    
    def _fix_common_issues(self, data: Dict) -> Dict:
        """修复常见的JSON结构问题"""
        # 检查是否是AI生成的直接模块格式 (如 {"登录功能": [...]})
        if "测试用例" not in data:
            # 如果有test_cases字段，直接使用
            if "test_cases" in data:
                data["测试用例"] = data["test_cases"]
            else:
                # 检查是否是直接的模块格式
                modules = []
                for key, value in data.items():
                    if isinstance(value, list) and key != "测试用例":
                        # 这是一个模块，转换为期望的格式
                        modules.append({key: value})
                
                if modules:
                    data = {"测试用例": modules}
                else:
                    data["测试用例"] = []
        
        # 确保测试用例是列表格式
        if not isinstance(data["测试用例"], list):
            data["测试用例"] = [data["测试用例"]]
        
        return data
    
    def _parse_from_plain_text(self, text: str) -> TestSuite:
        """从纯文本解析测试用例（兜底方案）"""
        self.logger.info("使用文本解析兜底方案")
        
        # 简单的文本解析逻辑
        lines = text.split('\n')
        test_cases = []
        current_case = None
        case_count = 1
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['测试用例', '用例', 'test case', 'tc']):
                if current_case:
                    test_cases.append(current_case)
                
                current_case = TestCase(
                    name=line if line else f"默认测试用例{case_count}",
                    case_id=f"TC_DEFAULT_{case_count:03d}",
                    test_type="功能测试",
                    test_level="系统测试", 
                    preconditions="系统正常运行",
                    steps=[TestStep(
                        description="执行测试操作",
                        expected_result="验证功能正常"
                    )]
                )
                case_count += 1
        
        if current_case:
            test_cases.append(current_case)
        
        # 创建默认模块
        module = TestModule(
            name="文本解析模块",
            test_cases=test_cases if test_cases else [TestCase(
                name="默认测试用例",
                case_id="TC_DEFAULT_001",
                test_type="功能测试",
                test_level="系统测试",
                preconditions="系统正常运行",
                steps=[TestStep(
                    description="执行基本功能测试",
                    expected_result="功能正常运行"
                )]
            )]
        )
        
        return TestSuite(modules=[module])
    
    def convert_to_legacy_format(self, test_suite: TestSuite) -> Dict[str, Any]:
        """转换为新的JSON格式"""
        new_format = {"测试用例": []}
        
        for module in test_suite.modules:
            module_cases = []
            for case in module.test_cases:
                # 🔥 使用新格式
                case_dict = {
                    "ID": case.case_id,
                    "用例名称": case.name,
                    "所属模块": case.module_name or module.name,
                    "前置条件": case.preconditions,
                    "备注": case.remark,
                    "步骤描述": "\n".join([step.description for step in case.steps]),
                    "预期结果": "\n".join([step.expected_result for step in case.steps]),
                    "编辑模式": case.edit_mode,
                    "标签": case.tag,
                    "用例等级": case.case_level,
                    "用例状态": case.case_status
                }
                
                module_cases.append(case_dict)
            
            new_format["测试用例"].append({module.name: module_cases})
        
        return new_format
    
    def validate_and_save(self, test_suite: TestSuite, output_file: str) -> bool:
        """验证并保存测试用例"""
        try:
            # 转换为新格式
            new_format = self.convert_to_legacy_format(test_suite)
            
            # 保存文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(new_format, f, ensure_ascii=False, indent=2)
            
            # 验证保存的文件
            with open(output_file, 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
            
            self.logger.info(f"✅ 测试用例已成功保存到 {output_file}")
            self.logger.info(f"📊 总计 {test_suite.total_cases} 个测试用例")
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存测试用例失败: {e}")
            return False

# ==================== 便捷函数 ====================

def parse_test_cases_from_text(text: str, output_file: str = None) -> Optional[Dict[str, Any]]:
    """
    便捷函数：从文本解析测试用例
    
    Args:
        text: 包含测试用例的文本
        output_file: 可选的输出文件路径
    
    Returns:
        解析后的测试用例字典，如果失败返回None
    """
    logger.info(f"🔧 parse_test_cases_from_text 开始解析，文本长度: {len(text)}")
    logger.info(f"🔧 文本预览: {text[:200]}...")
    
    parser = PydanticJSONParser()
    test_suite = parser.parse_test_cases_from_text(text)
    
    logger.info(f"🔧 Pydantic解析结果: {test_suite is not None}")
    if test_suite:
        logger.info(f"🔧 解析得到 {len(test_suite.modules)} 个模块，{test_suite.total_cases} 个测试用例")
        
        new_format = parser.convert_to_legacy_format(test_suite)
        logger.info(f"🔧 转换为新格式成功，键: {list(new_format.keys())}")
        logger.info(f"🔧 新格式测试用例数量: {len(new_format.get('测试用例', []))}")
        
        if output_file:
            parser.validate_and_save(test_suite, output_file)
        
        return new_format
    else:
        logger.warning("🔧 Pydantic解析失败，返回None")
    
    return None

def fix_json_parsing_issues(original_parser_function):
    """
    装饰器：为现有的JSON解析函数添加Pydantic解析支持
    
    使用方法：
    @fix_json_parsing_issues
    def your_existing_parser(text):
        # 原有的解析逻辑
        pass
    """
    def wrapper(text: str, *args, **kwargs):
        try:
            # 首先尝试原有的解析方法
            result = original_parser_function(text, *args, **kwargs)
            if result:
                return result
        except:
            pass
        
        # 如果原有方法失败，使用Pydantic解析
        logger.info("原有解析方法失败，尝试使用Pydantic解析")
        return parse_test_cases_from_text(text)
    
    return wrapper

# ==================== 示例使用 ====================

if __name__ == "__main__":
    # 测试解析器 - 新格式
    test_text_new = '''
    ```json
    {
        "测试用例": [
            {
                "登录模块": [
                    {
                        "ID": "TC_LOGIN_001",
                        "用例名称": "用户名密码登录",
                        "所属模块": "登录模块",
                        "前置条件": "系统正常运行，用户账号正常",
                        "备注": "基本登录功能测试",
                        "步骤描述": "输入正确的用户名和密码\n点击登录按钮\n验证登录结果",
                        "预期结果": "用户名和密码输入成功\n系统验证用户信息\n成功跳转到首页",
                        "编辑模式": "创建",
                        "标签": "功能测试",
                        "用例等级": "P1",
                        "用例状态": "待执行"
                    }
                ]
            }
        ]
    }
    ```
    '''
    
    # 测试解析器 - 旧格式
    test_text_old = '''
    ```json
    {
        "测试用例": [
            {
                "登录模块": [
                    {
                        "tc:用户名密码登录": {
                            "to": "TC_LOGIN_001",
                            "tt": "功能测试",
                            "ti": "系统测试",
                            "tp": "系统正常运行，用户账号正常",
                            "ts: 输入正确的用户名和密码": "ex:用户名和密码输入成功",
                            "ts: 点击登录按钮": "ex:系统验证用户信息",
                            "ts: 验证登录结果": "ex:成功跳转到首页"
                        }
                    }
                ]
            }
        ]
    }
    ```
    '''
    
    print("🧪 测试PydanticJSON解析器 - 新格式...")
    result_new = parse_test_cases_from_text(test_text_new, "test_output_new.json")
    
    print("🧪 测试PydanticJSON解析器 - 旧格式...")
    result_old = parse_test_cases_from_text(test_text_old, "test_output_old.json")
    
    if result_new:
        print("✅ 新格式解析成功！")
        print(json.dumps(result_new, ensure_ascii=False, indent=2)[:500] + "...")
    else:
        print("❌ 新格式解析失败")
        
    if result_old:
        print("✅ 旧格式解析成功！")
        print(json.dumps(result_old, ensure_ascii=False, indent=2)[:500] + "...")
    else:
        print("❌ 旧格式解析失败") 