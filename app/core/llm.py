import os
from langchain_openai import ChatOpenAI

from langchain.callbacks.base import BaseCallbackHandler
from typing import Dict, List, Any, Optional, AsyncGenerator
import asyncio

# 环境变量配置
OPENAI_BASE_URL = os.environ.get("OPENAI_BASE_URL")
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
MIDSCENE_MODEL_NAME = os.environ.get("MIDSCENE_MODEL_NAME")

class StreamingCallbackHandler(BaseCallbackHandler):
    """用于实时获取LLM响应的回调处理器"""
    
    def __init__(self):
        self.queue = asyncio.Queue()
        
    def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
        """当LLM产生新token时调用"""
        self.queue.put_nowait({"type": "token", "content": token})
        
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs: Any) -> None:
        """当LLM开始生成时调用"""
        self.queue.put_nowait({"type": "start"})
        
    def on_llm_end(self, response: Any, **kwargs: Any) -> None:
        """当LLM结束生成时调用"""
        self.queue.put_nowait({"type": "end"})
        
    def on_llm_error(self, error: Exception, **kwargs: Any) -> None:
        """当LLM出错时调用"""
        self.queue.put_nowait({"type": "error", "message": str(error)})
        
    async def aiter(self) -> AsyncGenerator[Dict[str, Any], None]:
        """异步迭代器，用于获取消息"""
        try:
            while True:
                message = await self.queue.get()
                yield message
                
                if message["type"] in ["end", "error"]:
                    break
        except asyncio.CancelledError:
            return

def get_chat_model(streaming: bool = True, temperature: float = 0.7):
    """
    获取聊天模型实例
    
    Args:
        streaming: 是否使用流式输出
        temperature: 温度参数
        
    Returns:
        配置好的聊天模型实例
    """
    streaming_handler = StreamingCallbackHandler() if streaming else None
    callbacks = [streaming_handler] if streaming_handler else None
    
    return ChatOpenAI(
        model=MIDSCENE_MODEL_NAME,
        temperature=temperature,
        openai_api_key=OPENAI_API_KEY,
        openai_api_base=OPENAI_BASE_URL,
        streaming=streaming,
        callbacks=callbacks
    ), streaming_handler 