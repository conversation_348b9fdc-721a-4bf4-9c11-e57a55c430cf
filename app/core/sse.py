import asyncio
import json
from typing import AsyncGenerator, Dict, Any, Callable, Optional, Union, List
from datetime import datetime


class SSEEventType:
    """SSE事件类型常量"""
    STATUS = "status"
    THINKING = "thinking"
    DECISION = "decision"
    TOOL_START = "tool_start"
    TOOL_RESULT = "tool_result"
    SUMMARY = "summary"
    ERROR = "error"
    COMPLETE = "complete"
    SESSION_INFO = "session_info"
    AGENT_START = "agent_start"
    CLOSE = "close"


async def sse_stream(generator: AsyncGenerator) -> AsyncGenerator[str, None]:
    """
    将异步生成器转换为SSE流
    
    Args:
        generator: 异步生成器，产生需要以SSE方式发送的数据
        
    Yields:
        SSE格式的数据流
    """
    try:
        async for data in generator:
            # 格式化数据为SSE格式
            sse_data = format_sse_data(data)
            if sse_data:
                yield sse_data
            
        # 发送完成事件
        yield format_sse_event(SSEEventType.COMPLETE, {"message": "流式传输完成"})
        
    except Exception as e:
        # 发送错误事件
        error_data = {
            "message": str(e),
            "timestamp": datetime.now().isoformat(),
            "type": "sse_error"
        }
        yield format_sse_event(SSEEventType.ERROR, error_data)
        
    finally:
        # 结束流
        yield format_sse_event(SSEEventType.CLOSE, "关闭流")


def format_sse_data(data: Union[Dict[str, Any], str, Any]) -> Optional[str]:
    """
    格式化数据为SSE格式
    
    Args:
        data: 要格式化的数据
        
    Returns:
        格式化后的SSE数据字符串，如果数据无效则返回None
    """
    try:
        if isinstance(data, dict):
            # 为字典数据添加时间戳（如果没有的话）
            if "timestamp" not in data:
                data["timestamp"] = datetime.now().isoformat()
            
            event_data = json.dumps(data, ensure_ascii=False)
            
            # 根据数据类型设置事件类型
            event_type = data.get("type", "data")
            
            return f"event: {event_type}\ndata: {event_data}\n\n"
            
        elif isinstance(data, str):
            # 纯文本数据
            return f"data: {data}\n\n"
        else:
            # 其他类型转换为字符串
            return f"data: {str(data)}\n\n"
            
    except Exception as e:
        # 如果格式化失败，返回错误信息
        error_data = json.dumps({
            "type": "format_error",
            "message": f"数据格式化失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        })
        return f"event: error\ndata: {error_data}\n\n"


def format_sse_event(event_type: str, data: Optional[Dict[str, Any]]) -> str:
    """
    格式化SSE事件
    
    Args:
        event_type: 事件类型
        data: 事件数据
        
    Returns:
        格式化后的SSE事件字符串
    """
    if data is None:
        return f"event: {event_type}\ndata: null\n\n"
    
    try:
        if not isinstance(data, dict):
            data = {"message": str(data)}
        
        # 添加时间戳
        if "timestamp" not in data:
            data["timestamp"] = datetime.now().isoformat()
        
        event_data = json.dumps(data, ensure_ascii=False)
        return f"event: {event_type}\ndata: {event_data}\n\n"
        
    except Exception as e:
        error_data = json.dumps({
            "type": "format_error",
            "message": f"事件格式化失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        })
        return f"event: error\ndata: {error_data}\n\n"


class SSEManager:
    """SSE管理器，用于处理复杂的SSE流式传输"""
    
    def __init__(self):
        self.active_streams: Dict[str, bool] = {}
        self.stream_stats: Dict[str, Dict[str, Any]] = {}
    
    async def create_agent_stream(
        self,
        stream_id: str,
        agent_generator: AsyncGenerator,
        heartbeat_interval: float = 30.0
    ) -> AsyncGenerator[str, None]:
        """
        创建智能体专用的SSE流
        
        Args:
            stream_id: 流ID
            agent_generator: 智能体生成器
            heartbeat_interval: 心跳间隔（秒）
            
        Yields:
            SSE格式的数据流
        """
        self.active_streams[stream_id] = True
        self.stream_stats[stream_id] = {
            "start_time": datetime.now(),
            "message_count": 0,
            "error_count": 0
        }
        
        try:
            # 发送流开始事件
            yield format_sse_event("stream_start", {
                "stream_id": stream_id,
                "message": "开始流式传输"
            })
            
            # 创建心跳任务
            heartbeat_task = asyncio.create_task(
                self._send_heartbeat(stream_id, heartbeat_interval)
            )
            
            # 处理智能体数据流
            async for data in agent_generator:
                if not self.active_streams.get(stream_id, False):
                    break
                
                sse_data = format_sse_data(data)
                if sse_data:
                    yield sse_data
                    self.stream_stats[stream_id]["message_count"] += 1
                    
                # 处理错误类型的数据
                if isinstance(data, dict) and data.get("type") == "error":
                    self.stream_stats[stream_id]["error_count"] += 1
            
            # 取消心跳任务
            heartbeat_task.cancel()
            
            # 发送完成事件
            yield format_sse_event("stream_complete", {
                "stream_id": stream_id,
                "stats": self.stream_stats[stream_id],
                "message": "流式传输完成"
            })
            
        except Exception as e:
            # 发送错误事件
            error_data = {
                "stream_id": stream_id,
                "message": f"流处理错误: {str(e)}",
                "stats": self.stream_stats.get(stream_id, {})
            }
            yield format_sse_event("stream_error", error_data)
            
        finally:
            # 清理
            self.active_streams.pop(stream_id, None)
            # 发送关闭事件
            yield format_sse_event("stream_close", {"stream_id": stream_id})
    
    async def _send_heartbeat(self, stream_id: str, interval: float):
        """发送心跳信号"""
        try:
            while self.active_streams.get(stream_id, False):
                await asyncio.sleep(interval)
                if self.active_streams.get(stream_id, False):
                    # 这里实际上不能直接yield，需要通过其他方式处理心跳
                    # 在实际应用中，可以通过队列或其他机制来处理
                    pass
        except asyncio.CancelledError:
            pass
    
    def stop_stream(self, stream_id: str) -> bool:
        """停止指定的流"""
        if stream_id in self.active_streams:
            self.active_streams[stream_id] = False
            return True
        return False
    
    def get_stream_stats(self, stream_id: str) -> Optional[Dict[str, Any]]:
        """获取流统计信息"""
        return self.stream_stats.get(stream_id)
    
    def get_active_streams(self) -> List[str]:
        """获取活跃的流列表"""
        return [stream_id for stream_id, active in self.active_streams.items() if active]


# 全局SSE管理器实例
sse_manager = SSEManager()


async def agent_sse_stream(
    agent_generator: AsyncGenerator,
    stream_id: Optional[str] = None
) -> AsyncGenerator[str, None]:
    """
    智能体专用的SSE流处理函数
    
    Args:
        agent_generator: 智能体生成器
        stream_id: 流ID，如果不提供则自动生成
        
    Yields:
        SSE格式的数据流
    """
    if not stream_id:
        import uuid
        stream_id = str(uuid.uuid4())
    
    async for sse_data in sse_manager.create_agent_stream(stream_id, agent_generator):
        yield sse_data 