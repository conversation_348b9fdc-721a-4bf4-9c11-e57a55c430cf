from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

import os
import uvicorn
import logging
from datetime import datetime
from pathlib import Path

from app.api.router import api_router

# 配置日志
def setup_logging():
    """配置应用的日志系统"""
    # 创建日志格式器
    log_formatter = logging.Formatter(
        fmt='%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(log_formatter)
    
    # 创建文件处理器（如果logs目录不存在则创建）
    logs_dir = "logs"
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
    
    file_handler = logging.FileHandler(
        filename=f"{logs_dir}/app_{datetime.now().strftime('%Y%m%d')}.log",
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(log_formatter)
    
    # 添加处理器到根日志记录器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # 配置第三方库的日志级别
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    
    # 应用级别的日志设置为DEBUG
    logging.getLogger("app").setLevel(logging.DEBUG)
    
    # 🔥 **配置AutoGen库的日志输出**
    try:
        from app.config.autogen_config import setup_autogen_logging
        setup_autogen_logging()
    except ImportError:
        # 如果AutoGen未安装，使用基础配置
        logging.getLogger("autogen_core").setLevel(logging.WARNING)
        logging.getLogger("autogen_core.events").setLevel(logging.WARNING)
        logging.getLogger("autogen_agentchat").setLevel(logging.WARNING)
        logging.getLogger("autogen_ext").setLevel(logging.WARNING)
        logging.getLogger("autogen").setLevel(logging.WARNING)
        print("⚠️ 使用基础AutoGen日志配置")
    
    logger = logging.getLogger(__name__)
    logger.info("🚀 日志系统初始化完成")
    logger.info(f"📁 日志文件路径: {logs_dir}/app_{datetime.now().strftime('%Y%m%d')}.log")

# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)

app = FastAPI(title="智能体平台")

logger.info("🏗️ 正在初始化 FastAPI 应用")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger.info("✅ CORS 中间件配置完成")

# 引入API路由
app.include_router(api_router)

logger.info("🛣️ API 路由配置完成")

# 配置静态文件服务 - 提供docs目录下的Excel文件下载
docs_dir = Path("docs")
docs_dir.mkdir(exist_ok=True)  # 确保docs目录存在

app.mount("/docs", StaticFiles(directory="docs"), name="docs")

logger.info("📁 静态文件服务配置完成 - docs目录可通过 /docs 访问")

if __name__ == "__main__":
    logger.info("🎬 启动智能体平台服务器")
    logger.info("🌐 服务器地址: http://0.0.0.0:8000")
    logger.info("🔄 热重载模式: 开启")
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True) 