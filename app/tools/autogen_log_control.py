#!/usr/bin/env python3
"""
AutoGen日志控制工具
用于快速开启、关闭或调整AutoGen库的日志输出级别
"""

import logging
import os
from typing import Literal

def set_autogen_log_level(level: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = "ERROR"):
    """
    设置AutoGen库的日志级别
    
    Args:
        level: 日志级别，可选值：DEBUG, INFO, WARNING, ERROR, CRITICAL
    """
    print(f"🔧 设置AutoGen日志级别为: {level}")
    
    # AutoGen相关的所有日志记录器
    autogen_loggers = [
        "autogen_core",              # AutoGen核心日志
        "autogen_core.events",       # AutoGen事件日志  
        "autogen_agentchat",         # AutoGen聊天日志
        "autogen_agentchat.agents",  # AutoGen智能体日志
        "autogen_agentchat.teams",   # AutoGen团队日志
        "autogen_ext",               # AutoGen扩展日志
        "autogen_ext.models",        # AutoGen模型日志
        "autogen",                   # AutoGen根命名空间
    ]
    
    log_level = getattr(logging, level.upper())
    
    for logger_name in autogen_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(log_level)
    
    print(f"✅ 已设置{len(autogen_loggers)}个AutoGen日志记录器的级别为{level}")

def suppress_autogen_logs():
    """完全抑制AutoGen日志输出"""
    print("🔇 完全抑制AutoGen日志输出")
    
    autogen_loggers = [
        "autogen_core", "autogen_core.events", "autogen_agentchat",
        "autogen_agentchat.agents", "autogen_agentchat.teams", 
        "autogen_ext", "autogen_ext.models", "autogen"
    ]
    
    for logger_name in autogen_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.CRITICAL)  # 只显示CRITICAL级别
        logger.propagate = False  # 禁用传播
    
    print(f"✅ 已完全抑制{len(autogen_loggers)}个AutoGen日志记录器")

def enable_autogen_logs(level: str = "INFO"):
    """启用AutoGen日志输出"""
    print(f"📝 启用AutoGen日志输出，级别: {level}")
    
    autogen_loggers = [
        "autogen_core", "autogen_core.events", "autogen_agentchat",
        "autogen_agentchat.agents", "autogen_agentchat.teams",
        "autogen_ext", "autogen_ext.models", "autogen"
    ]
    
    log_level = getattr(logging, level.upper())
    
    for logger_name in autogen_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(log_level)
        logger.propagate = True  # 启用传播
    
    print(f"✅ 已启用{len(autogen_loggers)}个AutoGen日志记录器，级别: {level}")

def show_autogen_loggers_status():
    """显示当前AutoGen日志记录器状态"""
    print("📊 AutoGen日志记录器状态:")
    
    autogen_loggers = [
        "autogen_core", "autogen_core.events", "autogen_agentchat",
        "autogen_agentchat.agents", "autogen_agentchat.teams",
        "autogen_ext", "autogen_ext.models", "autogen"
    ]
    
    for logger_name in autogen_loggers:
        logger = logging.getLogger(logger_name)
        level_name = logging.getLevelName(logger.level)
        propagate = "✅" if logger.propagate else "❌"
        print(f"  {logger_name}: {level_name} (传播: {propagate})")

def set_environment_variables(suppress: bool = True, log_level: str = "ERROR"):
    """
    设置环境变量来控制AutoGen日志
    
    Args:
        suppress: 是否抑制日志
        log_level: 日志级别
    """
    print("🌍 设置AutoGen日志控制环境变量")
    
    os.environ["AUTOGEN_LOG_LEVEL"] = log_level
    os.environ["AUTOGEN_SUPPRESS_LOGS"] = str(suppress)
    os.environ["AUTOGEN_SHOW_ERRORS_ONLY"] = "True"
    
    print(f"  AUTOGEN_LOG_LEVEL = {log_level}")
    print(f"  AUTOGEN_SUPPRESS_LOGS = {suppress}")
    print("  AUTOGEN_SHOW_ERRORS_ONLY = True")
    print("✅ 环境变量设置完成")

def interactive_control():
    """交互式AutoGen日志控制"""
    print("\n🎛️ AutoGen日志控制工具")
    print("="*50)
    
    while True:
        print("\n选择操作:")
        print("1. 完全抑制AutoGen日志")
        print("2. 设置AutoGen日志级别")
        print("3. 启用AutoGen日志")
        print("4. 查看当前状态")
        print("5. 设置环境变量")
        print("0. 退出")
        
        choice = input("\n请选择 (0-5): ").strip()
        
        if choice == "0":
            print("👋 退出AutoGen日志控制工具")
            break
        elif choice == "1":
            suppress_autogen_logs()
        elif choice == "2":
            print("\n可选日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL")
            level = input("请输入日志级别 (默认ERROR): ").strip().upper() or "ERROR"
            if level in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
                set_autogen_log_level(level)
            else:
                print("❌ 无效的日志级别")
        elif choice == "3":
            print("\n可选日志级别: DEBUG, INFO, WARNING, ERROR")
            level = input("请输入日志级别 (默认INFO): ").strip().upper() or "INFO"
            if level in ["DEBUG", "INFO", "WARNING", "ERROR"]:
                enable_autogen_logs(level)
            else:
                print("❌ 无效的日志级别")
        elif choice == "4":
            show_autogen_loggers_status()
        elif choice == "5":
            suppress = input("是否抑制日志? (y/N): ").strip().lower() == 'y'
            level = input("日志级别 (默认ERROR): ").strip().upper() or "ERROR"
            set_environment_variables(suppress, level)
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    # 命令行模式
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "suppress":
            suppress_autogen_logs()
        elif command == "enable":
            level = sys.argv[2] if len(sys.argv) > 2 else "INFO"
            enable_autogen_logs(level)
        elif command == "set":
            level = sys.argv[2] if len(sys.argv) > 2 else "ERROR"
            set_autogen_log_level(level)
        elif command == "status":
            show_autogen_loggers_status()
        else:
            print("❌ 未知命令")
            print("用法:")
            print("  python autogen_log_control.py suppress     # 抑制日志")
            print("  python autogen_log_control.py enable INFO  # 启用日志")
            print("  python autogen_log_control.py set ERROR    # 设置级别")
            print("  python autogen_log_control.py status       # 查看状态")
    else:
        # 交互式模式
        interactive_control() 