"""
测试用例Excel转换工具
将JSON格式的测试用例转换为Excel文件
"""

import json
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class TestCaseExcelConverter:
    """测试用例JSON转Excel转换器"""
    
    def __init__(self, output_dir: str = "docs"):
        """
        初始化转换器
        
        Args:
            output_dir: 输出目录，默认为docs
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
    def convert_json_to_excel(self, json_data: Dict[str, Any], filename: Optional[str] = None) -> str:
        """
        将JSON格式的测试用例转换为Excel文件
        
        Args:
            json_data: JSON格式的测试用例数据
            filename: 输出文件名，如果不指定则自动生成
            
        Returns:
            str: 生成的Excel文件路径
        """
        try:
            # 解析JSON数据，提取测试用例
            test_cases = self._extract_test_cases(json_data)
            
            if not test_cases:
                raise ValueError("未找到有效的测试用例数据")
            
            # 转换为DataFrame
            df = self._create_dataframe(test_cases)
            
            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"测试用例_{timestamp}.xlsx"
            
            # 确保文件名以.xlsx结尾
            if not filename.endswith('.xlsx'):
                filename += '.xlsx'
                
            # 完整文件路径
            file_path = self.output_dir / filename
            
            # 写入Excel文件
            self._write_to_excel(df, file_path)
            
            logger.info(f"成功生成Excel文件: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"转换JSON到Excel失败: {str(e)}")
            raise
    
    def _extract_test_cases(self, json_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从JSON数据中提取测试用例
        
        Args:
            json_data: JSON数据
            
        Returns:
            List[Dict]: 测试用例列表
        """
        test_cases = []
        
        # 处理不同的JSON结构
        if "测试用例" in json_data:
            # 新格式：{"测试用例": [{"功能模块1": [...]}]}
            for module_data in json_data["测试用例"]:
                for module_name, cases in module_data.items():
                    for case in cases:
                        case["所属模块"] = module_name
                        test_cases.append(case)
        else:
            # 旧格式：{"功能模块1": [...], "功能模块2": [...]}
            for module_name, cases in json_data.items():
                if isinstance(cases, list):
                    for case in cases:
                        case["所属模块"] = module_name
                        test_cases.append(case)
        
        logger.info(f"提取到 {len(test_cases)} 个测试用例")
        return test_cases
    
    def _create_dataframe(self, test_cases: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        将测试用例列表转换为DataFrame
        
        Args:
            test_cases: 测试用例列表
            
        Returns:
            pd.DataFrame: 转换后的DataFrame
        """
        # 定义Excel列顺序和中文列名
        column_mapping = {
            "ID": "用例ID",
            "用例名称": "用例名称",
            "所属模块": "所属模块",
            "前置条件": "前置条件",
            "步骤描述": "测试步骤",
            "预期结果": "预期结果",
            "备注": "备注",
            "标签": "标签",
            "用例等级": "用例等级",
            "用例状态": "用例状态",
            "编辑模式": "编辑模式"
        }
        
        # 创建DataFrame
        df_data = []
        for i, case in enumerate(test_cases, 1):
            row = {}
            # 如果没有ID，自动生成
            if "ID" not in case or not case["ID"]:
                case["ID"] = f"TC_{i:03d}"
                
            # 按照列映射顺序填充数据
            for eng_col, chinese_col in column_mapping.items():
                row[chinese_col] = case.get(eng_col, "")
                
            df_data.append(row)
        
        df = pd.DataFrame(df_data)
        
        # 处理换行符，Excel中使用\n表示换行
        for col in ["测试步骤", "预期结果"]:
            if col in df.columns:
                df[col] = df[col].astype(str).str.replace('\\n', '\n')
        
        return df
    
    def _write_to_excel(self, df: pd.DataFrame, file_path: Path):
        """
        将DataFrame写入Excel文件，并设置格式
        
        Args:
            df: 要写入的DataFrame
            file_path: 输出文件路径
        """
        try:
            # 使用ExcelWriter来设置格式
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 写入数据
                df.to_excel(writer, sheet_name='测试用例', index=False)
                
                # 获取工作表对象
                worksheet = writer.sheets['测试用例']
                
                # 设置列宽
                column_widths = {
                    'A': 12,  # 用例ID
                    'B': 25,  # 用例名称
                    'C': 15,  # 所属模块
                    'D': 20,  # 前置条件
                    'E': 40,  # 测试步骤
                    'F': 40,  # 预期结果
                    'G': 20,  # 备注
                    'H': 12,  # 标签
                    'I': 10,  # 用例等级
                    'J': 12,  # 用例状态
                    'K': 12   # 编辑模式
                }
                
                for col, width in column_widths.items():
                    worksheet.column_dimensions[col].width = width
                
                # 设置行高和自动换行
                from openpyxl.styles import Alignment
                for row in worksheet.iter_rows(min_row=2):  # 跳过标题行
                    for cell in row:
                        # 设置自动换行
                        cell.alignment = Alignment(wrap_text=True, vertical='top')
                        
                    # 设置行高（根据内容长度调整）
                    steps_cell = row[4]  # 测试步骤列
                    result_cell = row[5]  # 预期结果列
                    content_length = max(
                        len(str(steps_cell.value).split('\n')) if steps_cell.value else 1,
                        len(str(result_cell.value).split('\n')) if result_cell.value else 1
                    )
                    row_height = max(20, min(content_length * 15, 200))  # 限制最大高度
                    worksheet.row_dimensions[row[0].row].height = row_height
                
                # 设置标题行格式
                from openpyxl.styles import Font, PatternFill
                header_font = Font(bold=True, color='FFFFFF')
                header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
                
                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                
        except Exception as e:
            logger.error(f"写入Excel文件失败: {str(e)}")
            raise
    
    def get_available_files(self) -> List[Dict[str, Any]]:
        """
        获取docs目录下所有Excel文件列表
        
        Returns:
            List[Dict]: 文件信息列表
        """
        files = []
        try:
            for file_path in self.output_dir.glob("*.xlsx"):
                stat = file_path.stat()
                files.append({
                    "filename": file_path.name,
                    "filepath": str(file_path),
                    "size": stat.st_size,
                    "created_time": datetime.fromtimestamp(stat.st_ctime).strftime("%Y-%m-%d %H:%M:%S"),
                    "modified_time": datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                })
        except Exception as e:
            logger.error(f"获取文件列表失败: {str(e)}")
            
        return sorted(files, key=lambda x: x["modified_time"], reverse=True) 