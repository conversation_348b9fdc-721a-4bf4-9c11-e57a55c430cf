# AI智能体协作平台 - 前端应用

这是一个基于 Vue 3 的企业级AI智能体协作平台前端应用，支持多智能体团队协作、实时流式对话、会话管理和响应式设计。

## ✨ 功能特性

- 🤖 **智能体管理** - 浏览、选择和使用各种AI智能体
- 💬 **实时对话** - 支持流式输出的智能体对话体验
- 📝 **Markdown渲染** - 完整的Markdown内容渲染支持
- 💾 **会话管理** - 任务记录、会话切换和历史查询
- 🎨 **现代UI** - 基于Element Plus的精美界面设计
- 📱 **响应式设计** - 完美适配桌面端和移动端
- ⚡ **流式输出** - 实时展示AI思考过程和结果

## 🛠 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **Vue Router 4** - 官方路由管理器
- **Pinia** - 现代状态管理库
- **Element Plus** - Vue 3组件库
- **Markdown-it** - Markdown解析器
- **Highlight.js** - 代码高亮
- **RemixIcon** - 开源图标库
- **Vite** - 现代前端构建工具

## 📁 项目结构

```
front/
├── src/
│   ├── views/                     # 页面组件
│   │   ├── agent/
│   │   │   ├── AgentSelect.vue    # 智能体选择页面
│   │   │   ├── AgentChat.vue      # 智能体对话页面
│   │   │   └── summary.vue        # 对话总结页面
│   │   ├── home/
│   │   │   └── Home.vue           # 首页
│   │   ├── about/
│   │   │   └── About.vue          # 关于页面
│   │   └── other/                 # 其他页面（备用）
│   ├── components/                # 公共组件
│   │   ├── AppHeader.vue          # 应用头部
│   │   ├── AppLayout.vue          # 布局组件
│   │   ├── LoadingSpinner.vue     # 加载动画
│   │   └── markdownIt/            # Markdown渲染组件
│   ├── api/                       # API接口
│   ├── router/                    # 路由配置
│   ├── store/                     # 状态管理
│   ├── utils/                     # 工具函数
│   └── assets/                    # 静态资源
│       ├── css/                   # 样式文件
│       └── images/                # 图片资源
├── package.json
├── vite.config.js
└── index.html
```

## 🚀 快速开始

### 环境要求

- Node.js 16.0+
- npm 7.0+ 或 yarn 1.22+

### 安装依赖

```bash
cd front
npm install
```

### 启动开发服务器

```bash
npm run dev
```

应用将在 `http://localhost:3000` 启动，并自动打开浏览器。

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## 📄 主要页面

### 🏠 首页 (Home.vue)
- 平台介绍和导航
- 快速入口

### 🤖 智能体选择页面 (AgentSelect.vue)
- **功能特性**:
  - 智能体列表展示
  - 智能体信息预览
  - 使用统计显示
  - 任务输入和智能体选择
  - 一键开始对话

### 💬 智能体对话页面 (AgentChat.vue)
- **核心功能**:
  - 实时流式对话
  - Markdown内容渲染
  - 代码高亮显示
  - 任务记录管理
  - 会话历史切换
  - 新任务创建
  - 响应式布局

- **SSE事件支持**:
  - `session_info` - 会话信息
  - `agent_start` - 智能体开始
  - `thinking` - 思考过程
  - `decision` - 决策信息
  - `tool_start` - 工具开始
  - `tool_streaming` - 工具流式输出
  - `tool_result` - 工具结果
  - `summary` - 任务总结
  - `error` - 错误处理

### 📋 对话总结页面 (summary.vue)
- 对话内容总结
- 关键信息提取

## ⚙️ 配置说明

### Vite配置 (vite.config.js)

```javascript
{
  server: {
    port: 3000,                    // 开发服务器端口
    host: '0.0.0.0',              // 允许外部访问
    proxy: {
      '/api': {
        target: 'http://localhost:8000',  // 后端API地址
        changeOrigin: true
      }
    }
  }
}
```

### 路由配置

```javascript
const routes = [
  { path: '/', component: Home },              // 首页
  { path: '/agent', component: AgentSelect },  // 智能体选择
  { path: '/chat', component: AgentChat },     // 智能体对话
  { path: '/about', component: About }         // 关于页面
]
```

## 🎨 样式系统

### CSS变量支持
项目使用CSS变量系统，便于主题定制：

```css
:root {
  --primary-color: #2563eb;
  --text-primary: #1e293b;
  --bg-primary: #ffffff;
  --border-color: #e2e8f0;
}
```

### 响应式断点
- **移动端**: < 768px
- **平板端**: 768px - 1024px  
- **桌面端**: > 1024px

## 🔧 开发指南

### 组件开发规范
1. 使用Vue 3 Composition API
2. 遵循单文件组件(SFC)结构
3. 合理使用Props和Emits
4. 保持组件职责单一

### 代码风格
- 使用ESLint进行代码检查
- 遵循Vue官方风格指南
- 使用语义化的命名
- 添加必要的注释

### API调用
```javascript
// 示例：调用智能体API
import { agentApi } from '@/api'

const response = await agentApi.startChat({
  agent_id: 'agent_001',
  query: '用户问题',
  session_id: 'session_123'
})
```

## 🔌 API接口

### 主要接口
- `GET /agents` - 获取智能体列表
- `POST /chat/start` - 开始对话
- `GET /chat/stream` - SSE流式输出
- `GET /chat/history` - 获取对话历史
- `POST /chat/stop` - 停止对话

## 🎯 核心功能实现

### SSE流式输出
```javascript
// 建立SSE连接
const eventSource = new EventSource(`/api/chat/stream?session_id=${sessionId}`)

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data)
  handleStreamEvent(data)
}
```

### Markdown渲染
- 支持完整的Markdown语法
- 代码块高亮显示
- 表格、列表、链接等元素
- 自定义样式和主题

## 📦 依赖管理

### 主要依赖
```json
{
  "vue": "^3.3.0",
  "vue-router": "^4.2.0", 
  "pinia": "^2.1.0",
  "element-plus": "^2.3.0",
  "markdown-it": "^14.1.0",
  "highlight.js": "^11.11.1"
}
```

### 开发依赖
```json
{
  "@vitejs/plugin-vue": "^4.2.0",
  "vite": "^4.4.0",
  "vite-plugin-eslint": "^1.8.0"
}
```

## 🌐 浏览器支持

- Chrome 88+
- Firefox 78+
- Safari 14+
- Edge 88+

## 📱 移动端适配

- 响应式布局设计
- 触摸友好的交互
- 移动端优化的UI组件
- 侧边栏折叠功能

## 🔍 性能优化

- Vite构建优化
- 组件懒加载
- 图片资源优化
- 代码分割和压缩

## 🐛 调试指南

### 开发环境调试
1. 使用Vue DevTools
2. 浏览器开发者工具
3. Console日志输出
4. Network面板监控

### 常见问题
- SSE连接断开：检查网络状态和后端服务
- 样式异常：确认CSS变量和Element Plus主题
- 路由错误：检查路由配置和权限

## 🤝 贡献指南

1. Fork项目仓库
2. 创建功能分支 (`git checkout -b feature/新功能`)
3. 提交更改 (`git commit -m '添加新功能'`)
4. 推送到分支 (`git push origin feature/新功能`)
5. 创建Pull Request

## 📄 许可证

ISC License

## 👥 维护团队

如有问题或建议，请提交Issue或联系维护团队。

---

**快速链接:**
- [Vue 3 文档](https://cn.vuejs.org/)
- [Element Plus 文档](https://element-plus.org/zh-CN/)
- [Vite 文档](https://cn.vitejs.dev/)
- [Pinia 文档](https://pinia.vuejs.org/zh/) 