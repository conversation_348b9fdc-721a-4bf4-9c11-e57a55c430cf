// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'

// 请求配置
const REQUEST_CONFIG = {
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
}

// 通用请求函数
export const request = async (url, options = {}) => {
  const config = {
    ...REQUEST_CONFIG,
    ...options,
    headers: {
      ...REQUEST_CONFIG.headers,
      ...options.headers
    }
  }

  try {
    const response = await fetch(`${API_BASE_URL}${url}`, config)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    return data
  } catch (error) {
    console.error('API请求失败:', error)
    throw error
  }
}

// GET请求
export const get = (url, params = {}) => {
  const queryString = new URLSearchParams(params).toString()
  const fullUrl = queryString ? `${url}?${queryString}` : url
  
  return request(fullUrl, {
    method: 'GET'
  })
}

// POST请求
export const post = (url, data = {}) => {
  return request(url, {
    method: 'POST',
    body: JSON.stringify(data)
  })
}

// PUT请求
export const put = (url, data = {}) => {
  return request(url, {
    method: 'PUT',
    body: JSON.stringify(data)
  })
}

// DELETE请求
export const del = (url) => {
  return request(url, {
    method: 'DELETE'
  })
}

// 智能体相关API
export const agentApi = {
  // 获取智能体列表
  getAgents: () => get('/api/agent/list'),
  
  // 获取会话历史
  getSessionHistory: (data) => get('/api/agent/sessions', data),
  
  //执行任务 - SSE流式请求
  runTask: (data) => {
    return fetch(`${API_BASE_URL}/api/agent/run`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(data)
    })
  },

  // 停止会话
  stopSession: (data) => post('/api/agent/stop', data),
  
  // 上传文件并解析内容
  uploadFile: (file) => {
    const formData = new FormData()
    formData.append('file', file)
    
    return fetch(`${API_BASE_URL}/api/agent/upload-file`, {
      method: 'POST',
      body: formData
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return response.json()
    })
  },
  
  // 获取支持的文件类型
  getSupportedFileTypes: () => get('/api/agent/supported-file-types'),
  
}

// SSE连接函数
export const createSSEConnection = (url, options = {}) => {
  const eventSource = new EventSource(`${API_BASE_URL}${url}`, options)
  
  return {
    eventSource,
    onMessage: (callback) => {
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          callback(data)
        } catch (error) {
          console.error('SSE数据解析失败:', error)
          callback({ type: 'error', message: '数据解析失败' })
        }
      }
    },
    onError: (callback) => {
      eventSource.onerror = callback
    },
    close: () => {
      eventSource.close()
    }
  }
}

// 智能体对话SSE连接
export const createAgentChatSSE = (sessionId, onMessage, onError) => {
  const sseConnection = createSSEConnection(`/api/sessions/${sessionId}/stream`)
  
  sseConnection.onMessage(onMessage)
  sseConnection.onError(onError)
  
  return sseConnection
}

// 错误处理
export const handleApiError = (error) => {
  console.error('API错误:', error)
  
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    return '网络连接失败，请检查网络状态'
  }
  
  if (error.message.includes('HTTP error')) {
    const status = error.message.match(/status: (\d+)/)?.[1]
    switch (status) {
      case '400':
        return '请求参数错误'
      case '401':
        return '未授权访问'
      case '403':
        return '访问被拒绝'
      case '404':
        return '资源不存在'
      case '500':
        return '服务器内部错误'
      default:
        return '请求失败'
    }
  }
  
  return error.message || '未知错误'
}

export default {
  request,
  get,
  post,
  put,
  del,
  agentApi,
  createSSEConnection,
  createAgentChatSSE,
  handleApiError
} 