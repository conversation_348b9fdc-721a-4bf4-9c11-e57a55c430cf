/* AI智能体管理平台样式 */
.zp-agent {
    --primary-color: #3296ff;
    --primary-light: #6e92f5;
    --primary-color-V1: #3a6df0;
    --success-color-V1: #3af0b9;
    --warning-color-V1: #f0b93a;
    --error-color-V1: #f56c6c;
    --info-color-V1: #a03af0;
  
    --primary-gradient: linear-gradient(120deg, #3a6df0, #4f46e5);
    --success-gradient: linear-gradient(120deg, #34d399, #059669);
    --warning-gradient: linear-gradient(120deg, #fbbf24, #d97706);
    --error-gradient: linear-gradient(120deg, #ef4444, #b91c1c);
  
    /* 自动化测试智能体 */
    --auto-primary-btn-color: #3a6df0;
    --auto-success-btn-color: #34d399;
    --auto-warning-btn-color: #fbbf24;
    --auto-error-btn-color: #ef4444;
    --auto-debug-btn-color: #6366f1;
  
    /* --primary-gradient: linear-gradient(120deg, #3296ff, #1e6fd9); */
    --secondary-color: #4f46e5;
    --accent-color: #7a86fb;
    --background-color: #f7f9fc;
    --card-bg-color: #ffffff;
    --text-color: #1a2b4b;
    --text-light: #5d7290;
    --border-color: #f0f2f5;
    --success-color: #06d6a0;
    --warning-color: #ffd166;
    --error-color: #ef476f;
    --shadow-sm: 0 2px 6px rgba(58, 109, 240, 0.04);
    --shadow: 0 4px 10px rgba(58, 109, 240, 0.06);
    --shadow-lg: 0 6px 14px rgba(58, 109, 240, 0.08);
    --shadow-card: 0 8px 16px rgba(58, 109, 240, 0.06);
    --radius-sm: 5px;
    --radius: 10px;
    --radius-lg: 12px;
    --transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
    --header-height: 56px;
    --primary-color-rgb: 58, 109, 240;
    --font-size-detail: 0.9rem;
  }
  
  /* 深色模式 */
  .zp-agent .dark-mode {
    --primary-color: #4d7aff;
    --primary-light: #7a97ff;
    --primary-gradient: linear-gradient(120deg, #4d7aff, #6366f1);
    --secondary-color: #6366f1;
    --accent-color: #8a91ff;
    --background-color: #0f172a;
    --card-bg-color: #1e293b;
    --text-color: #f1f5f9;
    --text-light: #94a3b8;
    --border-color: #2a3649;
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.2);
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.22);
    --shadow-lg: 0 6px 16px rgba(0, 0, 0, 0.25);
    --shadow-card: 0 8px 20px rgba(0, 0, 0, 0.25);
    --primary-color-rgb: 77, 122, 255;
  }
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family:
      'Inter',
      'SF Pro Display',
      'PingFang SC',
      -apple-system,
      BlinkMacSystemFont,
      sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.5;
    font-weight: 400;
    letter-spacing: 0.01em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color 0.3s ease;
  }
  
  .agent-container {
    max-width: 1220px;
    margin: 0 auto;
    padding: 0 1.5rem;
    padding-bottom: 20px;
    /* height: 100%; */
    /* display: flex;
    flex-direction: column;
    overflow: hidden; */
  }
  
  .subtitle {
    color: var(--text-light);
    font-size: 1rem;
    margin-bottom: 1rem;
  }
  
  /* 头部导航 */
  .header {
    background-color: var(--card-bg-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: var(--transition);
  }
  
  .nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: var(--header-height);
  }
  
  .logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--primary-color);
    letter-spacing: -0.01em;
    transition: var(--transition);
  }
  
  .logo:hover {
    transform: translateY(-1px);
  }
  
  .logo img {
    height: 28px;
    margin-right: 0.4rem;
    transition: var(--transition);
  }
  
  .nav-links {
    display: flex;
    gap: 2rem;
  }
  
  .nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
    transition: var(--transition);
  }
  
  .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background: var(--primary-gradient);
    transition: var(--transition);
    opacity: 0;
  }
  
  .nav-link:hover,
  .nav-link.active {
    color: var(--primary-color);
  }
  
  .nav-link:hover::after,
  .nav-link.active::after {
    width: 100%;
    opacity: 1;
  }
  
  .user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .user-avatar {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: var(--shadow-sm);
    border: 2px solid transparent;
    transition: var(--transition);
  }
  
  .user-avatar:hover {
    border-color: var(--primary-light);
    transform: scale(1.05);
  }
  
  /* 主内容区 */
  /* .main {
    padding: 2rem 0;
    min-height: calc(100vh - var(--header-height) - 80px);
  } */
  
  /* 卡片组件 */
  .card {
    background-color: var(--card-bg-color);
    border-radius: var(--radius);
    box-shadow: var(--shadow-card);
    transition: var(--transition);
    overflow: hidden;
    border: none;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
  
  .card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
  }
  
  /* 智能体卡片 */
  .agent-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.25rem;
    /* margin-top: 1.25rem; */
  }
  
  .agent-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    overflow: hidden;
  }
  
  .agent-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: transparent;
    transition: var(--transition);
  }
  
  .agent-card:hover::before {
    background: var(--primary-gradient);
  }
  
  .agent-card-header {
    padding: 1.2rem;
    display: flex;
    align-items: center;
  }
  
  .agent-icon {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-sm);
    margin-right: 0.9rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }
  
  .agent-card:hover .agent-icon {
    transform: scale(1.08);
  }
  
  .agent-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
  }
  
  .agent-name {
    font-weight: 600;
    font-size: 1.1rem;
    flex: 1;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  .agent-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--success-color);
    box-shadow: 0 0 8px var(--success-color);
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(6, 214, 160, 0.6);
    }
  
    70% {
      box-shadow: 0 0 0 5px rgba(6, 214, 160, 0);
    }
  
    100% {
      box-shadow: 0 0 0 0 rgba(6, 214, 160, 0);
    }
  }
  
  .agent-status.offline {
    background-color: var(--text-light);
    box-shadow: none;
    animation: none;
  }
  
  .agent-card-body {
    padding: 0.5rem 1.25rem 1.25rem;
    flex: 1;
  }
  
  .agent-description {
    color: var(--text-light);
    margin-bottom: 1rem;
  
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.5;
    font-size: 0.9rem;
    height: 45px;
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
    display: -webkit-box; /* 兼容 WebKit 内核浏览器 */
    -webkit-line-clamp: 2; /* 限制显示行数（根据 max-height 调整） */
    -webkit-box-orient: vertical; /* 垂直方向排列 */
  }
  
  .agent-stats {
    display: flex;
    align-items: center;
    color: var(--text-light);
    font-size: 0.8rem;
    gap: 0.5rem;
  }
  
  .agent-card-footer {
    padding: 0.9rem 1.2rem;
    display: flex;
    justify-content: space-between;
    gap: 0.3rem;
  }
  
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0.8rem;
    border-radius: var(--radius-sm);
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    letter-spacing: 0.01em;
    position: relative;
    overflow: hidden;
    text-decoration: none;
  }
  
  .btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
  }
  
  .btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
  }
  
  @keyframes ripple {
    0% {
      transform: scale(0, 0);
      opacity: 0.5;
    }
  
    100% {
      transform: scale(30, 30);
      opacity: 0;
    }
  }
  
  .btn-icon {
    padding: 0.4rem;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    background-color: var(--background-color);
    color: var(--text-color);
  }
  
  .btn-icon:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
  }
  
  /* 搜索和筛选 */
  .search-bar {
    background-color: var(--card-bg-color);
    padding: 1.25rem 1.5rem;
    border-radius: var(--radius);
    box-shadow: var(--shadow-card);
    margin-bottom: 2rem;
    border: none;
  }
  
  .search-form {
    display: flex;
    gap: 0.8rem;
  }
  
  .search-input {
    flex: 1;
    padding: 0.8rem 1.2rem;
    border: none;
    border-radius: var(--radius-sm);
    font-size: 0.95rem;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: var(--transition);
  }
  
  .search-input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(58, 109, 240, 0.15);
  }
  
  .filter-group {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
  }
  
  .filter-tag {
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 14px;
    background-color: var(--background-color);
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
    border: none;
  }
  
  .filter-tag:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
    transform: translateY(-2px);
  }
  
  .filter-tag.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 3px 8px rgba(58, 109, 240, 0.25);
  }
  
  /* 详情页 */
  .agent-detail-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.75rem;
    padding: 1.25rem;
    background-color: var(--card-bg-color);
    border-radius: var(--radius);
    box-shadow: var(--shadow-card);
    border: none;
  }
  
  .agent-detail-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius);
    margin-right: 1.25rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
  }
  
  .agent-detail-info {
    flex: 1;
  }
  
  .agent-detail-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.4rem;
    margin-top: 0.3rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
  }
  
  .agent-detail-meta {
    display: flex;
    gap: 1.25rem;
    color: var(--text-light);
    font-size: var(--font-size-detail);
    flex-wrap: wrap;
  }
  
  .agent-detail-meta span {
    display: flex;
    align-items: center;
    gap: 0.4rem;
  }
  
  .agent-detail-meta i {
    color: var(--primary-color);
  }
  
  .agent-detail-actions {
    display: flex;
    gap: 0.8rem;
  }
  
  .tabs {
    display: flex;
    margin-bottom: 1.5rem;
    gap: 0.5rem;
  }
  
  .tab {
    padding: 0.6rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: var(--transition);
    color: var(--text-light);
    background-color: transparent;
  }
  
  .tab:hover {
    color: var(--primary-color);
    background-color: rgba(var(--primary-color-rgb), 0.05);
  }
  
  .tab.active {
    color: white;
    background: var(--primary-gradient);
    font-weight: 600;
    box-shadow: 0 3px 8px rgba(58, 109, 240, 0.25);
  }
  
  .tab-content {
    padding: 0.8rem 0;
    margin-bottom: 0;
  }
  
  /* 聊天界面 */
  .chat-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    height: calc(100vh - var(--header-height));
    background-color: var(--background-color);
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow-card);
  }
  
  .chat-sidebar {
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--card-bg-color);
    overflow: hidden;
    z-index: 20;
    border-right: 1px solid var(--border-color);
  }
  
  .chat-sidebar.mobile {
    position: absolute;
    height: calc(100vh - var(--header-height));
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    top: var(--header-height);
    left: 0;
    width: 280px;
  }
  
  .chat-sidebar.mobile.active {
    transform: translateX(0);
  }
  
  .overlay {
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 15;
    opacity: 0;
    visibility: hidden;
    transition:
      opacity 0.3s ease,
      visibility 0.3s ease;
  }
  
  .overlay.active {
    opacity: 1;
    visibility: visible;
  }
  
  .chat-sections-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding-bottom: 1rem;
  }
  
  .chat-sidebar-header {
    padding: 0.6rem;
    display: flex;
    justify-content: center;
    background-color: var(--card-bg-color);
    border-bottom: 1px solid var(--border-color);
  }
  
  .chat-sections-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
  }
  
  .chat-section-tab {
    flex: 1;
    padding: 0.7rem 0;
    display: flex;
    justify-content: center;
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--text-light);
    cursor: pointer;
    position: relative;
    transition: var(--transition);
  }
  
  .chat-section-tab.active {
    color: var(--primary-color);
  }
  
  .chat-section-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 20%;
    right: 20%;
    height: 2px;
    background-color: var(--primary-color);
  }
  
  .chat-section-title {
    padding: 0.8rem 0.9rem;
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
    letter-spacing: 0.02em;
  }
  
  .chat-section {
    overflow-y: visible;
    display: block;
    padding: 0.75rem;
    background-color: var(--card-bg-color);
  }
  
  .chat-history {
  }
  
  .chat-session {
    padding: 0.8rem !important;
    border-radius: var(--radius-sm) !important;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 0.5rem;
    border: none;
    background-color: var(--background-color) !important;
    box-shadow: var(--shadow-sm) !important;
  }
  
  .chat-session:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05) !important;
    transform: translateX(3px) !important;
  }
  
  .chat-session.active {
    background-color: rgba(var(--primary-color-rgb), 0.1) !important;
    border-left: 3px solid var(--primary-color) !important;
  }
  
  .chat-session-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 0.2rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-color) !important;
  }
  
  .chat-session-time {
    font-size: 0.75rem;
    color: var(--text-light);
  }
  
  .chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--background-color);
    position: relative;
    max-width: 100vw;
  }
  
  .chat-header {
    padding: 0.75rem 1.25rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--card-bg-color);
    border-bottom: 1px solid var(--border-color);
  }
  
  .chat-title {
    display: flex;
    align-items: center;
  }
  
  .chat-title-icon {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-sm);
    margin-right: 0.8rem;
    box-shadow: var(--shadow-sm);
  }
  
  .chat-title-name {
    font-weight: 600;
    font-size: 1rem;
    color: var(--primary-color-V1);
  }
  
  .chat-actions {
    display: flex;
    gap: 0.6rem;
  }
  
  .chat-messages {
    flex: 1;
    padding: 1.25rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
  }
  
  .message {
    display: flex;
    max-width: 90%;
    animation: fadeIn 0.5s ease;
    position: relative;
    margin-bottom: 20px;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
  
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .message.user {
    align-self: flex-end;
  }
  
  .message.agent {
    align-self: flex-start;
  }
  
  .message.system {
    align-self: center;
    max-width: 70%;
  }
  
  .message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 0.8rem;
    box-shadow: var(--shadow-sm);
    border: 2px solid white;
    object-fit: cover;
  }
  
  .message.user .message-avatar {
    order: 1;
    margin-right: 0;
    margin-left: 0.8rem;
  }
  
  .message-content {
    background-color: var(--card-bg-color);
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    line-height: 1.4;
    font-size: 14px;
  }
  
  .message.user .message-content {
    /* background: var(--primary-color); */
    background: var(--primary-gradient);
    border-radius: var(--radius);
    color: white;
    border-radius: 8px;
    border: none;
  }
  
  .message.system .message-content {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--text-light);
    font-size: 14px;
    text-align: center;
  }
  
  /* Markdown内容样式 */
  /* .message-content p {
    margin-bottom: 0.5rem;
  }
  
  .message-content p:last-child {
    margin-bottom: 0;
  }
  
  .message-content pre {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    overflow-x: auto;
    font-size: 14px;
    margin: 0.5rem 0;
    border: none !important;
  }
  
  .message-content code {
    padding: 0.2rem 0.4rem;
    border-radius: var(--radius-sm);
    font-size: 14px;
    font-family: monospace;
  }
  
  .message-content ul,
  .message-content ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
  }
  
  .message-content img {
    max-width: 100%;
    border-radius: var(--radius-sm);
    margin: 0.5rem 0;
  }
  
  .message-content h1,
  .message-content h2,
  .message-content h3,
  .message-content h4 {
    margin-top: 0.8rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
  }
  
  .message-content blockquote {
    border-left: 3px solid var(--primary-light);
    padding-left: 0.8rem;
    margin: 0.5rem 0;
    color: var(--text-light);
  } */
  
  .message-time {
    font-size: 0.7rem;
    color: var(--text-light);
    margin-top: 0.3rem;
  }
  
  /* 保留其他 Markdown 相关样式 */
  .chat-input-container {
    background-color: var(--card-bg-color);
    border-top: 1px solid var(--border-color);
    position: relative;
    z-index: 20;
    width: 100%;
    bottom: 0;
    left: 0;
    right: 0;
  }
  
  .chat-input-form {
    display: flex;
    gap: 0.8rem;
    position: relative;
    align-items: center;
    width: 100%;
  }
  
  .chat-input {
    flex: 1;
    padding: 0.8rem 1.2rem;
    padding-bottom: 2.2rem;
    border: none;
    border-radius: 12px;
    resize: none;
    min-height: 120px;
    max-height: 150px;
    font-family: inherit;
    font-size: 14px;
    background-color: #ffffff;
    color: var(--text-color);
    transition: var(--transition);
    overflow: auto;
    box-shadow: inset 0 0 0 1px var(--border-color);
    width: 100%;
    display: block;
    line-height: 1.8;
  }
  
  .chat-input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(58, 109, 240, 0.15);
  }
  
  .chat-input-actions {
    position: absolute;
    bottom: 0.5rem;
    left: 1rem;
    display: flex;
    gap: 0.5rem;
    z-index: 2;
  }
  
  .btn-send {
    min-width: 42px;
    height: 42px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 3px 8px rgba(58, 109, 240, 0.25);
    transition: var(--transition);
    border: none;
    font-size: 1.1rem;
    flex-shrink: 0;
  }
  
  .btn-send:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 5px 12px rgba(58, 109, 240, 0.35);
  }
  
  .btn-send:active {
    transform: translateY(0) scale(0.98);
  }
  
  /* 会话历史项和智能体列表项样式 */
  .session-history-item {
    padding: 0.8rem;
    border-radius: var(--radius-sm);
    margin-bottom: 0.6rem;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
    color: var(--text-color);
    background-color: var(--background-color);
    box-shadow: var(--shadow-sm);
  }
  
  .session-history-item:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    transform: translateY(-2px);
  }
  
  .session-history-item .time {
    font-size: 0.7rem;
    color: var(--text-light);
    margin-top: 0.2rem;
  }
  
  .agent-list-item {
    display: flex;
    align-items: center;
    padding: 0.8rem;
    border-radius: var(--radius-sm);
    margin-bottom: 0.6rem;
    cursor: pointer;
    transition: var(--transition);
    background-color: var(--background-color);
    box-shadow: var(--shadow-sm);
  }
  
  .agent-list-item:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    transform: translateX(3px);
  }
  
  .agent-list-item.active {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-left: 3px solid var(--primary-color);
  }
  
  .agent-list-icon {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-sm);
    margin-right: 0.7rem;
  }
  
  .agent-list-info {
    flex: 1;
  }
  
  .agent-list-name {
    font-weight: 500;
    font-size: 14px;
    color: var(--text-color);
  }
  
  .agent-list-description {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-top: 0.2rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* 响应式设计 */
  @media (max-width: 1200px) {
    .chat-messages {
      max-width: 800px;
    }
  }
  
  @media (max-width: 992px) {
    .chat-messages {
      max-width: 100%;
      padding: 1rem;
    }
  }
  
  @media (max-width: 768px) {
    .chat-container {
      grid-template-columns: 1fr;
    }
  
    .chat-sidebar:not(.mobile) {
      display: none;
    }
  
    .chat-header {
      padding: 0.7rem 1rem;
    }
  
    .chat-title-name {
      font-size: 0.9rem;
    }
  
    .chat-title-icon {
      width: 28px;
      height: 28px;
    }
  
    .message {
      max-width: 90%;
    }
  
    .message-avatar {
      width: 28px;
      height: 28px;
    }
  
    .sidebar-toggle {
      display: flex !important;
    }
  }
  
  @media (max-width: 576px) {
    .chat-input-container {
      padding: 0.7rem;
    }
  
    .chat-input {
      font-size: 0.9rem;
      padding: 0.7rem 1rem;
      padding-bottom: 2rem;
      min-height: 50px;
    }
  
    .btn-send {
      min-width: 40px;
      height: 40px;
      font-size: 1rem;
    }
  
    .message {
      max-width: 95%;
    }
  
    .chat-main {
      display: flex;
      flex-direction: column;
      height: calc(100vh - var(--header-height));
    }
  
    .chat-messages {
      flex: 1;
      overflow-y: auto;
    }
  }
  
  .message-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px;
    bottom: 0;
    right: 0;
    border-radius: 0 0 8px 8px;
    width: 100%;
  }
  
  .message-actions-left {
    display: flex;
    align-items: center;
  }
  
  .message-actions-right {
    display: flex;
    gap: 8px;
    align-items: center;
  }
  
  .message-action-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: var(--text-light);
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: transparent;
    border: none;
    padding: 0;
  }
  
  .message-action-btn:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
    transform: translateY(-1px);
  }
  
  .message-action-btn i {
    font-size: 18px;
  }
  
  .stop-output-btn-card {
    height: 32px;
    background-color: rgb(219 234 254);
    color: var(--primary-color);
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--stop-button-border);
    padding: 4px 16px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
  }
  
  .stop-output-btn-card:hover {
    background-color: rgb(160 195 241);
    color: #4d6bfe;
    transform: translateY(-1px);
  }
  
  .modern-markdown .message-action-btn {
    width: 34px;
    height: 34px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .modern-markdown .message-action-btn:hover {
    transform: translateY(-2px) !important;
  }
  
  .message-reactions {
    position: absolute;
    right: 0;
    bottom: -30px;
    display: flex;
    gap: 0.4rem;
    opacity: 1;
    transition: opacity 0.2s ease;
  }
  
  .message:hover .message-reactions {
    opacity: 1;
  }
  
  .message.user .message-actions {
    left: -40px;
    right: auto;
    top: 0;
    bottom: auto;
    flex-direction: column;
  }
  
  .message.user .message-reactions {
    right: auto;
    left: 0;
  }
  
  .message-reaction-btn {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    background-color: var(--card-bg-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.8rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }
  
  .message-reaction-btn:hover {
    transform: translateY(-2px);
  }
  
  .message-reaction-btn.liked {
    color: var(--primary-color);
  }
  
  .message-reaction-btn.disliked {
    color: var(--error-color);
  }
  
  /* 滚动条样式美化 */
  .chat-sections-container::-webkit-scrollbar,
  .chat-messages::-webkit-scrollbar,
  .chat-input::-webkit-scrollbar {
    width: 6px;
  }
  
  .chat-sections-container::-webkit-scrollbar-track,
  .chat-messages::-webkit-scrollbar-track,
  .chat-input::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .chat-sections-container::-webkit-scrollbar-thumb,
  .chat-messages::-webkit-scrollbar-thumb,
  .chat-input::-webkit-scrollbar-thumb {
    background-color: rgba(var(--primary-color-rgb), 0.2);
    border-radius: 10px;
  }
  
  .chat-sections-container::-webkit-scrollbar-thumb:hover,
  .chat-messages::-webkit-scrollbar-thumb:hover,
  .chat-input::-webkit-scrollbar-thumb:hover {
    background-color: rgba(var(--primary-color-rgb), 0.3);
  }
  
  /* 标签样式 */
  .agent-tags {
    display: flex;
    flex-wrap: wrap;
    /* margin: 0.5rem 0; */
    gap: 0.4rem;
  }
  
  .agent-tag {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
  }
  
  .agent-tag:hover {
    background-color: rgba(var(--primary-color-rgb), 0.2);
  }
  
  /* 标签颜色变体 */
  .tag-blue {
    background-color: rgba(58, 109, 240, 0.1);
    color: #3a6df0;
  }
  
  .tag-green {
    background-color: rgba(6, 214, 160, 0.1);
    color: #06d6a0;
  }
  
  .tag-purple {
    background-color: rgba(130, 80, 223, 0.1);
    color: #8250df;
  }
  
  .tag-orange {
    background-color: rgba(255, 161, 22, 0.1);
    color: #ff9900;
  }
  
  .tag-red {
    background-color: rgba(239, 71, 111, 0.1);
    color: #ef476f;
  }
  
  .tag-teal {
    background-color: rgba(20, 184, 166, 0.1);
    color: #14b8a6;
  }
  
  .tag-indigo {
    background-color: rgba(99, 102, 241, 0.1);
    color: #6366f1;
  }
  
  .tag-cyan {
    background-color: rgba(6, 182, 212, 0.1);
    color: #06b6d4;
  }
  
  .tag-pink {
    background-color: rgba(244, 114, 182, 0.1);
    color: #f472b6;
  }
  
  .tag-yellow {
    background-color: rgba(250, 204, 21, 0.1);
    color: #facc15;
  }
  
  /* 无结果提示 */
  .no-results {
    text-align: center;
    padding: 4rem 0;
    background-color: var(--card-bg-color);
    border-radius: var(--radius);
    margin-top: 2rem;
    box-shadow: var(--shadow-sm);
  }
  
  .no-results-icon {
    font-size: 3rem;
    color: var(--text-light);
    display: block;
    margin-bottom: 1rem;
  }
  
  .no-results h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }
  
  .no-results p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
  }
  
  /* 按钮颜色变体 - 基于现有主按钮和副按钮设计 */
  
  /* 主按钮 */
  .btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 3px 8px rgba(58, 109, 240, 0.25);
    text-decoration: none;
  }
  
  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(58, 109, 240, 0.35);
  }
  
  .btn-outline {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    color: var(--primary-color-V1);
    text-decoration: none;
  }
  
  .btn-outline:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color-V1);
    transform: translateY(-2px);
  }
  
  /* 成功/绿色按钮 */
  .btn-success {
    background: linear-gradient(120deg, #10b981, #059669);
    color: white;
    box-shadow: 0 3px 8px rgba(16, 185, 129, 0.25);
    text-decoration: none;
  }
  
  .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(16, 185, 129, 0.35);
  }
  
  .btn-success-outline {
    background-color: rgba(16, 185, 129, 0.05);
    color: #059669;
    text-decoration: none;
    border: 1px solid rgba(16, 185, 129, 0.2);
  }
  
  .btn-success-outline:hover {
    background-color: rgba(16, 185, 129, 0.1);
    color: #047857;
    transform: translateY(-2px);
    border-color: rgba(16, 185, 129, 0.3);
  }
  
  /* 危险/红色按钮 */
  .btn-danger {
    background: linear-gradient(120deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 3px 8px rgba(239, 68, 68, 0.25);
    text-decoration: none;
  }
  
  .btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(239, 68, 68, 0.35);
  }
  
  .btn-danger-outline {
    background-color: rgba(239, 68, 68, 0.05);
    color: #dc2626;
    text-decoration: none;
    border: 1px solid rgba(239, 68, 68, 0.2);
  }
  
  .btn-danger-outline:hover {
    background-color: rgba(239, 68, 68, 0.1);
    color: #b91c1c;
    transform: translateY(-2px);
    border-color: rgba(239, 68, 68, 0.3);
  }
  
  /* 警告/橙色按钮 */
  .btn-warning {
    background: linear-gradient(120deg, #f59e0b, #d97706);
    color: white;
    box-shadow: 0 3px 8px rgba(245, 158, 11, 0.25);
    text-decoration: none;
  }
  
  .btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(245, 158, 11, 0.35);
  }
  
  .btn-warning-outline {
    background-color: rgba(245, 158, 11, 0.05);
    color: #d97706;
    text-decoration: none;
    border: 1px solid rgba(245, 158, 11, 0.2);
  }
  
  .btn-warning-outline:hover {
    background-color: rgba(245, 158, 11, 0.1);
    color: #b45309;
    transform: translateY(-2px);
    border-color: rgba(245, 158, 11, 0.3);
  }
  
  /* 紫色按钮 */
  .btn-purple {
    background: linear-gradient(120deg, #8b5cf6, #7c3aed);
    color: white;
    box-shadow: 0 3px 8px rgba(139, 92, 246, 0.25);
    text-decoration: none;
  }
  
  .btn-purple:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(139, 92, 246, 0.35);
  }
  
  .btn-purple-outline {
    background-color: rgba(139, 92, 246, 0.05);
    color: #7c3aed;
    text-decoration: none;
    border: 1px solid rgba(139, 92, 246, 0.2);
  }
  
  .btn-purple-outline:hover {
    background-color: rgba(139, 92, 246, 0.1);
    color: #6d28d9;
    transform: translateY(-2px);
    border-color: rgba(139, 92, 246, 0.3);
  }
  
  /* 青色按钮 */
  .btn-cyan {
    background: linear-gradient(120deg, #06b6d4, #0891b2);
    color: white;
    box-shadow: 0 3px 8px rgba(6, 182, 212, 0.25);
    text-decoration: none;
  }
  
  .btn-cyan:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(6, 182, 212, 0.35);
  }
  
  .btn-cyan-outline {
    background-color: rgba(6, 182, 212, 0.05);
    color: #0891b2;
    text-decoration: none;
    border: 1px solid rgba(6, 182, 212, 0.2);
  }
  
  .btn-cyan-outline:hover {
    background-color: rgba(6, 182, 212, 0.1);
    color: #0e7490;
    transform: translateY(-2px);
    border-color: rgba(6, 182, 212, 0.3);
  }
  
  /* 粉色按钮 */
  .btn-pink {
    background: linear-gradient(120deg, #ec4899, #db2777);
    color: white;
    box-shadow: 0 3px 8px rgba(236, 72, 153, 0.25);
    text-decoration: none;
  }
  
  .btn-pink:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(236, 72, 153, 0.35);
  }
  
  .btn-pink-outline {
    background-color: rgba(236, 72, 153, 0.05);
    color: #db2777;
    text-decoration: none;
    border: 1px solid rgba(236, 72, 153, 0.2);
  }
  
  .btn-pink-outline:hover {
    background-color: rgba(236, 72, 153, 0.1);
    color: #be185d;
    transform: translateY(-2px);
    border-color: rgba(236, 72, 153, 0.3);
  }
  
  /* 灰色按钮 */
  .btn-gray {
    background: linear-gradient(120deg, #6b7280, #4b5563);
    color: white;
    box-shadow: 0 3px 8px rgba(107, 114, 128, 0.25);
    text-decoration: none;
  }
  
  .btn-gray:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(107, 114, 128, 0.35);
  }
  
  .btn-gray-outline {
    background-color: rgba(107, 114, 128, 0.05);
    color: #4b5563;
    text-decoration: none;
    border: 1px solid rgba(107, 114, 128, 0.2);
  }
  
  .btn-gray-outline:hover {
    background-color: rgba(107, 114, 128, 0.1);
    color: #374151;
    transform: translateY(-2px);
    border-color: rgba(107, 114, 128, 0.3);
  }
  