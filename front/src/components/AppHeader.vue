<template>
  <el-header class="app-header">
    <div class="header-content">
        <div class="logo">
          <i class="ri-robot-line logo-icon"></i>
        
          <span class="logo-text" >AI智能体测试平台</span>
        </div>
      <el-menu
        mode="horizontal"
        :default-active="activeIndex"
        @select="handleSelect"
        class="nav-menu"
      >
        <el-menu-item index="1">首页</el-menu-item>
        <el-menu-item index="2">智能体</el-menu-item>
        <el-menu-item index="3">测试管理</el-menu-item>
        <el-menu-item index="4">UI自动化</el-menu-item>
        <el-menu-item index="5">关于我们</el-menu-item>
      </el-menu>
    </div>
  </el-header>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

export default {
  name: 'AppHeader',

  setup() {
    const router = useRouter()
    const route = useRoute()
    
    // 根据当前路由设置激活的菜单项
    const activeIndex = computed(() => {
      switch (route.name) {
        case 'Home':
          return '1'
        case 'AgentSelect':
        case 'AgentChat':
          return '2'
        case 'About':
          return '5'
        default:
          return '1'
      }
    })

    const handleSelect = (key) => {
      switch (key) {
        case '1':
          router.push('/')
          break
        case '2':
          router.push('/agent')
          break
        case '3':
          router.push('/')
          break
        case '4':
          router.push('/')
          break
        case '5':
          router.push('/about')
          break
      }
    }

    return {
      activeIndex,
      handleSelect
    }
  }
}
</script>

<style scoped>
@import '@/assets/css/style.css';
.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.logo-text {
  color: var(--primary-color);
}

.logo-icon {
  font-size: 24px;
  color: var(--primary-color);
}

.nav-menu {
  background: transparent;
  border: none;
}

.nav-menu .el-menu-item {
  color: #333;
  font-weight: 500;
}

.nav-menu .el-menu-item:hover {
  color: #409eff;
}

.nav-menu .el-menu-item.is-active {
  color: #409eff;
  border-bottom-color: #409eff;
}
</style> 