import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/home/<USER>'
import About from '@/views/about/About.vue'
import AgentSelect from '@/views/agent/AgentSelect.vue'
import AgentChat from '@/views/agent/AgentChat.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/agent',
    name: 'AgentSelect',
    component: AgentSelect,
    meta: {
      title: '选择智能体'
    }
  },
  {
    path: '/chat',
    name: 'AgentChat',
    component: AgentChat,
    meta: {
      title: '智能体对话'
    }
  },

  {
    path: '/about',
    name: 'About',
    component: About,
    meta: {
      title: '关于我们'
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - AI智能体协作平台`
  }
  next()
})

export default router 