import { defineStore } from 'pinia'

// 主应用状态
export const useAppStore = defineStore('app', {
  state: () => ({
    // 应用配置
    config: {
      title: 'AI智能体协作平台',
      version: '1.0.0'
    },
    // 用户信息
    user: {
      name: '',
      avatar: '',
      isLogin: false
    },
    // 加载状态
    loading: false,
    // 侧边栏状态
    sidebarCollapsed: false
  }),
  
  getters: {
    // 获取用户信息
    getUserInfo: (state) => state.user,
    // 获取应用配置
    getAppConfig: (state) => state.config,
    // 获取加载状态
    isLoading: (state) => state.loading
  },
  
  actions: {
    // 设置用户信息
    setUser(userInfo) {
      this.user = { ...this.user, ...userInfo }
    },
    
    // 设置登录状态
    setLoginStatus(status) {
      this.user.isLogin = status
    },
    
    // 设置加载状态
    setLoading(status) {
      this.loading = status
    },
    
    // 切换侧边栏状态
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },
    
    // 登出
    logout() {
      this.user = {
        name: '',
        avatar: '',
        isLogin: false
      }
    }
  }
})

// 智能体对话状态
export const useChatStore = defineStore('chat', {
  state: () => ({
    // 对话历史
    messages: [],
    // 当前会话ID
    currentSessionId: null,
    // 可用的智能体列表
    agents: [],
    // 当前选中的智能体
    currentAgent: null,
    // 连接状态
    isConnected: false,
    // 初始消息（从选择页面传递过来）
    initialMessage: null
  }),
  
  getters: {
    // 获取对话消息
    getMessages: (state) => state.messages,
    // 获取当前智能体
    getCurrentAgent: (state) => state.currentAgent,
    // 获取连接状态
    getConnectionStatus: (state) => state.isConnected
  },
  
  actions: {
    // 添加消息
    addMessage(message) {
      this.messages.push({
        id: Date.now(),
        timestamp: new Date().toISOString(),
        ...message
      })
    },
    
    // 设置智能体列表
    setAgents(agents) {
      this.agents = agents
    },
    
    // 选择智能体
    selectAgent(agent) {
      this.currentAgent = agent
    },
    
    // 设置会话ID
    setSessionId(sessionId) {
      this.currentSessionId = sessionId
    },
    
    // 设置连接状态
    setConnectionStatus(status) {
      this.isConnected = status
    },
    
    // 清空对话
    clearMessages() {
      this.messages = []
    },
    
    // 设置初始消息
    setInitialMessage(message) {
      this.initialMessage = message
    },
    
    // 获取并清除初始消息
    getAndClearInitialMessage() {
      const message = this.initialMessage
      this.initialMessage = null
      return message
    }
  }
}) 