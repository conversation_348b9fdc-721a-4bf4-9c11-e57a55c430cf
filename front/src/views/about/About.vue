<template>
  <div class="about">
    <!-- 导航头部 -->
    <div class="about-header">
      <el-button text @click="goBack">
        <i class="ri-arrow-left-line"></i>
        返回
      </el-button>
      <h1>关于我们</h1>
    </div>

    <!-- 主要内容 -->
    <div class="about-content">
      <div class="content-container">
        <!-- 项目介绍 -->
        <el-card class="section-card">
          <template #header>
            <div class="card-header">
              <i class="ri-information-line"></i>
              <span>项目介绍</span>
            </div>
          </template>
          <div class="project-intro">
            <h2>AI智能体测试平台</h2>
            <p>
              这是一个基于AutoGen v0.6和ReAct循环的企业级智能体协作平台，
              支持多智能体团队协作、实时流式输出、会话管理和可扩展架构。
            </p>
            <p>
              平台旨在通过智能体之间的协作，提供高效的问题解决方案，
              特别适用于需求分析、测试用例生成、代码审查等场景。
            </p>
          </div>
        </el-card>

        <!-- 技术架构 -->
        <el-card class="section-card">
          <template #header>
            <div class="card-header">
              <i class="ri-settings-3-line"></i>
              <span>技术架构</span>
            </div>
          </template>
          <div class="tech-architecture">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12" :md="8">
                <div class="tech-layer">
                  <h4>前端技术</h4>
                  <ul>
                    <li>Vue 3.3+</li>
                    <li>Element Plus</li>
                    <li>Pinia状态管理</li>
                    <li>Vue Router</li>
                    <li>Vite构建工具</li>
                  </ul>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="tech-layer">
                  <h4>后端技术</h4>
                  <ul>
                    <li>Python 3.8+</li>
                    <li>FastAPI</li>
                    <li>AutoGen v0.6</li>
                    <li>LangChain</li>
                    <li>SSE流式通信</li>
                  </ul>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="tech-layer">
                  <h4>AI框架</h4>
                  <ul>
                    <li>Microsoft AutoGen</li>
                    <li>ReAct循环</li>
                    <li>OpenAI兼容API</li>
                    <li>阿里云通义千问</li>
                    <li>智能体协作</li>
                  </ul>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 核心特性 -->
        <el-card class="section-card">
          <template #header>
            <div class="card-header">
              <i class="ri-star-line"></i>
              <span>核心特性</span>
            </div>
          </template>
          <div class="core-features">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12" :md="6">
                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="ri-team-line"></i>
                  </div>
                  <h4>多智能体协作</h4>
                  <p>支持多个智能体团队协作，实现复杂任务的分工处理</p>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="6">
                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="ri-flashlight-line"></i>
                  </div>
                  <h4>实时流式输出</h4>
                  <p>基于SSE技术，提供实时的智能体思考和执行过程展示</p>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="6">
                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="ri-chat-4-line"></i>
                  </div>
                  <h4>会话管理</h4>
                  <p>完整的会话生命周期管理，支持多用户会话隔离</p>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="6">
                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="ri-puzzle-line"></i>
                  </div>
                  <h4>可扩展架构</h4>
                  <p>基于AutoGen框架，支持灵活的智能体扩展</p>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 应用场景 -->
        <el-card class="section-card">
          <template #header>
            <div class="card-header">
              <i class="ri-compass-3-line"></i>
              <span>应用场景</span>
            </div>
          </template>
          <div class="use-cases">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12">
                <div class="use-case-item">
                  <h4>软件测试</h4>
                  <ul>
                    <li>自动化测试用例生成</li>
                    <li>需求分析和测试计划制定</li>
                    <li>测试结果分析和报告</li>
                    <li>回归测试策略制定</li>
                  </ul>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12">
                <div class="use-case-item">
                  <h4>代码审查</h4>
                  <ul>
                    <li>代码质量分析</li>
                    <li>安全漏洞检测</li>
                    <li>性能优化建议</li>
                    <li>最佳实践推荐</li>
                  </ul>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12">
                <div class="use-case-item">
                  <h4>需求分析</h4>
                  <ul>
                    <li>业务需求梳理</li>
                    <li>功能点分解</li>
                    <li>技术方案设计</li>
                    <li>风险评估分析</li>
                  </ul>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12">
                <div class="use-case-item">
                  <h4>文档生成</h4>
                  <ul>
                    <li>API文档自动生成</li>
                    <li>用户手册编写</li>
                    <li>技术规范制定</li>
                    <li>项目文档维护</li>
                  </ul>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>


      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'
import { useAppStore } from '@/store'

export default {
  name: 'About',
  setup() {
    const router = useRouter()
    const appStore = useAppStore()

    const goBack = () => {
      router.push('/')
    }

    const openGitHub = () => {
      window.open('https://github.com/your-repo/ai-agent-demo', '_blank')
    }

    const openDocs = () => {
      window.open('/docs', '_blank')
    }

    return {
      goBack,
      openGitHub,
      openDocs
    }
  }
}
</script>

<style scoped>
.about {
  min-height: 100vh;
  background: #fafbfc;
}

.about-header {
  height: 60px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: none;
  position: sticky;
  top: 0;
  z-index: 100;
}

.about-header h1 {
  margin: 0 0 0 20px;
  color: #2c3e50;
  font-size: 24px;
}

.about-content {
  padding: 20px;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: none;
  border: 1px solid #f0f0f0;
  background: #ffffff;
  transition: all 0.3s ease;
}

.section-card:hover {
  border-color: #e0e6ff;
  transform: translateY(-1px);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.card-header i {
  color: #409eff;
  font-size: 18px;
}

.project-intro h2 {
  color: #409eff;
  margin-bottom: 16px;
}

.project-intro p {
  line-height: 1.8;
  color: #666;
  margin-bottom: 16px;
}

.tech-architecture {
  padding: 10px 0;
}

.tech-layer {
  text-align: center;
  padding: 20px;
  background: #fafbfc;
  border-radius: 10px;
  height: 100%;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.tech-layer:hover {
  background: #f5f7fa;
  border-color: #e0e6ff;
}

.tech-layer h4 {
  color: #409eff;
  margin-bottom: 16px;
}

.tech-layer ul {
  list-style: none;
  padding: 0;
}

.tech-layer li {
  padding: 4px 0;
  color: #666;
  border-bottom: 1px solid #eee;
}

.tech-layer li:last-child {
  border-bottom: none;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-item {
  text-align: center;
  padding: 24px;
  background: #fafbfc;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
  background: #f5f7fa;
  border-color: #e0e6ff;
}

.feature-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
}

.feature-icon i {
  font-size: 48px;
}

.feature-item h4 {
  color: #2c3e50;
  margin-bottom: 12px;
}

.feature-item p {
  color: #666;
  line-height: 1.6;
}

.use-cases {
  padding: 20px 0;
}

.use-cases .el-timeline-item__content {
  padding-left: 20px;
}

.use-cases .el-card {
  border: 1px solid #f0f0f0;
  box-shadow: none;
  border-radius: 10px;
}

.use-cases h4 {
  color: #409eff;
  margin-bottom: 8px;
}

.use-cases p {
  color: #666;
  line-height: 1.6;
}

.version-info {
  padding: 10px 0;
}

.contact-info {
  padding: 10px 0;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background: #fafbfc;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  height: 100%;
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: #f5f7fa;
  border-color: #e0e6ff;
  transform: translateY(-1px);
}

.contact-item i {
  font-size: 18px;
  color: #409eff;
  margin-right: 8px;
}

.contact-item h4 {
  color: #2c3e50;
  margin-bottom: 8px;
}

.contact-item p {
  color: #666;
  line-height: 1.6;
}

.use-case-item {
  padding: 24px;
  background: #fafbfc;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  height: 100%;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.use-case-item:hover {
  background: #f5f7fa;
  border-color: #e0e6ff;
  transform: translateY(-1px);
}

.use-case-item h4 {
  color: #409eff;
  margin-bottom: 16px;
}

.use-case-item ul {
  list-style: none;
  padding: 0;
}

.use-case-item li {
  padding: 6px 0;
  color: #666;
  position: relative;
  padding-left: 20px;
}

.use-case-item li:before {
  content: "•";
  color: #409eff;
  position: absolute;
  left: 0;
}

.dev-docs {
  text-align: center;
  padding: 20px 0;
}

.dev-docs p {
  color: #666;
  line-height: 1.8;
  margin-bottom: 30px;
}

.doc-links {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.doc-links .el-button i {
  margin-right: 8px;
}

.about-header .el-button i {
  margin-right: 8px;
}

@media (max-width: 768px) {
  .about-content {
    padding: 10px;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .tech-layer {
    margin-bottom: 20px;
  }
  
  .contact-item {
    flex-direction: column;
    text-align: center;
  }
}
</style> 