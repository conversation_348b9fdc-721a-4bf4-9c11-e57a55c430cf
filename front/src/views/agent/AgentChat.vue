<template>

  <div class="zp-agent zp-chat">
    <!-- 聊天容器 -->
    <div class="chat-container">
      <!-- 聊天侧边栏 -->
      <div class="chat-sidebar" id="chatSidebar">
        <div class="chat-sidebar-header" style="margin-bottom: 0rem !important; padding: 0.75rem">
          <button class="btn btn-primary" style="width: 100%; justify-content: center; height: 32px" @click="clickNewTask()">
            <i class="ri-add-line" style="margin-right: 0.5rem"></i>
            新任务
          </button>
        </div>

        <!-- 重新组织的侧边栏内容区域，使用 flex 布局 -->
        <div class="chat-sections-container">
          <!-- 任务记录区域 -->
          <div class="chat-section-wrapper history-section">
            <div class="chat-section-title" style="font-size: 16px">任务记录</div>
            <div class="chat-section scrollable" id="history">
              <div class="chat-history">
                <div class="chat-session" :class="{ active: item.session_id == currentConversationId }" v-for="item in historyConversationsList" :key="item.session_id" @click="clickHistoryConversation(item)">
                  <div class="chat-session-title">{{ item.agent_name }}</div>
                  <div class="chat-session-time">{{ formatTimestamp(item.created_at) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天主区域 -->
      <div class="chat-main">
        <div class="chat-header">
          <div class="chat-title">
            <button class="btn btn-icon sidebar-toggle" id="sidebarToggle" style="margin-right: 0.5rem; display: none">
              <i class="ri-menu-line"></i>
            </button>
     
            <!-- <img :src="currentAgent.image" alt="" class="chat-title-icon" /> -->
            <div class="chat-title-name"><i class="ri-speak-ai-fill speck-icon" ></i>{{ currentAgent.name }}</div>
          </div>
        </div>

        <div class="chat-messages">
          <!-- 系统消息 -->
          <div class="message system" v-if="historyMessages.length > 0">
            <div class="message-content" style="border-radius: 10px">
              <markdown-renderer :content="'**' + formatTimestamp(historyMessages[0].created_at) + ' · 对话创建**'" messageType="system"></markdown-renderer>
            </div>
          </div>

          <!-- 历史消息渲染 -->
          <template v-for="(message, index) in historyMessages" :key="'msg-' + index">
            <!-- 用户消息 (query) -->
            <div class="message user" :key="'query-' + message.id" v-if="message.query">
              <div>
                <div class="message-content message-content-user">
                  <pre class="message-content-user-query" style="">{{ message.query }}</pre>
                  <!-- 添加用户消息中的图片展示 -->
                  <div class="user-message-images" v-if="message.images && message.images.length > 0">
                    <div v-for="(image, index) in message.images" :key="'img-' + index" class="user-message-image-item">
                      <img :src="image.preview" :alt="image.name" class="user-message-img" @click="showImagePreview(image.preview)" />
                    </div>
                  </div>
                </div>

                <div class="message-actions">
                  <div class="message-actions-left">
                    <div class="message-action-btn" title="复制消息" @click="copyToClipboard(message.query, 'user')">
                      <i class="ri-file-copy-line"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 智能体回复 (answer) -->
            <div class="message agent" :key="'answer-' + message.id + '-' + (message.status || 'default')" v-if="message.answer || message.messageType" :data-status="message.status" :class="{ 'message-special': message.messageType }">
              <div class="message-content-wrapper">
                <div class="agent-content-wrapper">
                  <div
                    class="message-content message-content-agent"
                    :class="{
                      streaming: message.status === 'streaming',
                      'streaming-active': message.status === 'streaming' && message.answer.length > 0,
                      [`message-type-${message.messageType}`]: message.messageType
                    }">
                    <!-- 流式状态指示器 -->
                    <div v-if="message.status === 'streaming'" class="streaming-indicator">
                      <!-- <div class="streaming-dots">
                        <span class="dot"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                      </div> -->
                      <span class="streaming-text">{{ getStreamingText(message.messageType) }}</span>
                      <!-- 流式进度条 -->
                      <div class="streaming-progress">
                        <div class="streaming-progress-bar"></div>
                      </div>
                    </div>
                    <markdown-renderer :content="processDeepThinking(message.answer)" messageType="agent" :is-streaming="message.status === 'streaming'"></markdown-renderer>

                    <!-- 添加引用资源列表 -->
                    <div class="retriever-resources" v-if="message.retrieverResources && message.retrieverResources.length > 0">
                      <div class="retriever-resources-title">
                        引用
                        <div class="retriever-resources-title-icon" style="background-color: #eaebed; height: 1px"></div>
                      </div>
                      <div class="retriever-resources-list">
                        <div class="retriever-resource-item" v-for="(resource, index) in message.retrieverResources" :key="index">
                          <div class="retriever-resource-item-title">
                            <a :href="resource.url" target="_blank">{{ resource.document_name }}</a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </template>
        </div>
        <!-- 输入内容，图片上传预览 -->
        <div class="chat-input-container" :class="{ isStreaming: isStreaming }">
          <div class="image-previews-wrapper" v-if="uploadFile.length > 0">
            <div class="image-previews-container">
              <div v-for="(file, index) in uploadFile" :key="file.id" class="image-preview-item">
                <div class="image-preview">
                  <img :src="file.preview" alt="上传图片" class="preview-img" />
                  <div class="image-preview-name" :title="file.name">{{ file.name }}</div>
                  <div class="image-preview-actions">
                    <button class="image-action-btn" @click="removeUploadedImage(index)">
                      <i class="ri-close-line"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="uploadingImage" class="image-preview-item uploading">
            <div class="image-preview">
              <img :src="previewImage" alt="预览图片" class="preview-img" />
              <div class="image-preview-overlay">
                <i class="ri-loader-line loading-icon"></i>
                <span class="upload-text">上传中...</span>
              </div>
            </div>
          </div>

          <textarea class="chat-input" placeholder="输入您的问题或指令..." rows="2" v-model="inputMessage" @keyup.ctrl.enter="sendMessage()" @keydown.enter.exact.prevent="handleEnterPress" @paste="handlePaste" @compositionstart="isComposing = true" @compositionend="isComposing = false"></textarea>
          <div class="chat-input-actions-container">
            <div class="chat-input-actions">
              <div class="chat-input-actions-left" style="padding-left: 30px">
                <!-- 文件上传 -->
                <input type="file" ref="documentInput" style="display: none" :accept="acceptedFileTypes" @change="handleDocumentSelected" />
                <button type="button" class="btn btn-icon" :class="{ 'btn-disabled': uploadingDocument }" style="font-size: 1.5rem; padding: 0.3rem" :title="uploadingDocument ? '解析中...' : '上传文档'" @click="triggerDocumentInput" :disabled="uploadingDocument">
                  <i :class="uploadingDocument ? 'ri-loader-line loading-spin' : 'ri-attachment-line'"></i>
                </button>
                
                <!-- 图片上传 -->
                <input type="file" ref="fileInput" style="display: none" accept="image/*" multiple @change="handleFileSelected" />
                <button v-if="isShowImageUpload" type="button" class="btn btn-icon" :class="{ 'btn-disabled': uploadFile.length >= 3 }" style="font-size: 1.5rem; padding: 0.3rem" :title="uploadFile.length >= 3 ? '已达最大上传数量' : '上传图片'" @click="triggerFileInput" :disabled="uploadFile.length >= 3">
                  <i class="ri-image-add-line"></i>
                </button>
              </div>

              <button type="button" class="btn-send" :class="{ 'btn-stop': isStreaming }" @click="isStreaming ? stopStreaming() : sendMessage()">
                <i :class="isStreaming ? 'ri-stop-fill' : 'ri-send-plane-fill'"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 总结概要区域 -->
      <div class="params-section" v-if="showSummary">
        <div class="params-header">
          <div class="params-title"></div>
        </div>
        <div class="params-content">
          <summaryInfo ref="summary" :summary-content="summaryContent" :is-streaming="isSummaryStreaming"></summaryInfo>
        </div>
      </div>
    </div>
  </div>

</template>
<script>
import MarkdownRenderer from '@/components/markdownIt/MarkdownRendererAgent.vue'
import summaryInfo from '@/views/agent/summary.vue'
import AppLayout from '@/components/AppLayout.vue'
import { useAppStore, useChatStore } from '@/store/index.js'
import { agentApi } from '@/api/index.js'
export default {
  name: 'AgentChat',
  components: {
    MarkdownRenderer,
    summaryInfo,
    AppLayout
  },
  data() {
    return {
      name: '',

      // 推荐智能体列表
      recommendedAgents: [],
      // 当前智能体
      currentAgentId: '',
      currentAgent: {},
      // 历史会话列表
      historyConversationsList: [],
      // 历史消息列表
      historyMessages: [],
      // 当前会话ID
      currentConversationId: '',

      messages: [],
      currentReader: null,

      inputMessage: '',
      isStreaming: false,

      // 图片上传相关
      uploadingImage: false,
      previewImage: null,
      uploadFile: [],
      isShowImageUpload: false,
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewImage: '',
      //输入法
      isComposing: false,

      // 摘要相关
      summaryContent: '',
      showSummary: false,
      isSummaryStreaming: false,
      
      // 文件上传相关
      supportedFileTypes: [],
      acceptedFileTypes: '',
      uploadingDocument: false
    }
  },
  methods: {
    //获取智能体列表
    async getAgentList() {
      try {
        let res = await agentApi.getAgents()

        if (res && res.agents) {
          this.recommendedAgents = res.agents
          this.recommendedAgents.forEach((agent, index) => {
            if (agent.id == this.currentAgentId) {
              this.currentAgent = agent
            }
          })
        }
      } catch (error) {
        console.error('获取智能体列表失败:', error)
      }
    },



    //获取历史会话列表
    async getHistoryConversationsList() {
      let res = await agentApi.getSessionHistory({user_id: this.name})
      if(res && res.sessions){
        this.historyConversationsList = res.sessions
      }
    },

    // 获取支持的文件类型
    async getSupportedFileTypes() {
      try {
        const res = await agentApi.getSupportedFileTypes()
        if (res && res.supported_types) {
          this.supportedFileTypes = res.supported_types
          this.acceptedFileTypes = res.supported_types.join(',')
        }
      } catch (error) {
        console.error('获取支持的文件类型失败:', error)
      }
    },

    //点击新任务
    clickNewTask() {
      this.$router.push({
        path: '/agent'
      })
      this.resetSummary()
    },



    // 发送消息
    sendMessage(content, type = 'agent', showTimestamp = true) {
      // 检查 content 是否是事件对象，如果是则忽略
      if (content && typeof content === 'object' && content.isTrusted !== undefined) {
        content = null // 如果传入的是事件对象，则设为 null
      }

      // 如果没有输入内容且没有传入content，则返回
      if (!this.inputMessage.trim() && !content) return

      // 如果正在流式传输中，则不允许发送新消息
      if (this.isStreaming) return

      // 使用传入的content或者inputMessage
      const message = content || this.inputMessage.trim()

      // 如果是新话题，重置摘要
      this.resetSummary()

      // 将之前所有的流式消息状态设置为完成状态，避免干扰新的对话轮次
      this.historyMessages.forEach(msg => {
        if (msg.status === 'streaming') {
          msg.status = 'completed'
        }
      })

      // 创建用户消息对象
      const userMessage = {
        id: Date.now() + '_user_' + Math.random().toString(36).substring(2, 8),
        query: message,
        answer: '',
        status: 'completed',
        created_at: new Date().toISOString(),
        images: [...this.uploadFile] // 复制上传的图片
      }

      // 创建智能体回复消息对象
      const agentMessage = {
        id: Date.now() + '_agent_' + Math.random().toString(36).substring(2, 8),
        query: '',
        answer: '',
        status: 'streaming',
        created_at: new Date().toISOString(),
        retrieverResources: []
      }

      // 添加消息到历史记录
      this.historyMessages.push(userMessage)
      this.historyMessages.push(agentMessage)

      // 清空输入和上传文件
      this.inputMessage = ''
      this.uploadFile = []
      this.isStreaming = true

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })

      // 发送SSE请求
      this.sendSSERequest(message, agentMessage)
    },

    // 发送SSE请求
    async sendSSERequest(query, messageObj) {
      try {
        // 准备请求数据
        const requestData = {
          agent_id: this.currentAgentId,
          query: query,
          session_id: this.currentConversationId,
          user_id: this.name // 这里可以从用户信息中获取
        }

        // 创建SSE连接
        const response = await agentApi.runTask(requestData)
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        console.log('SSE响应:', response)
        console.log('响应状态:', response.status)
        console.log('响应头:', response.headers)

        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let buffer = ''

        // 保存当前reader，用于可能的停止操作
        this.currentReader = reader

        while (true) {
          const { done, value } = await reader.read()

          if (done) {
            // 流结束，更新状态
            messageObj.status = 'completed'
            this.isStreaming = false
            this.currentReader = null
            break
          }

          // 解码数据
          buffer += decoder.decode(value, { stream: true })

          // 处理SSE数据
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // 保留不完整的行

          // 解析SSE事件
          this.parseSSELines(lines, messageObj)

          // 强制Vue响应式更新
          this.$forceUpdate()
          
          // 滚动到底部
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        }
      } catch (error) {
        console.error('SSE请求失败:', error)
        messageObj.status = 'error'
        messageObj.answer = '抱歉，发生了错误，请稍后重试。'
        this.isStreaming = false
        this.currentReader = null
      }
    },

    // 解析SSE行数据
    parseSSELines(lines, messageObj) {
      let currentEvent = null
      let currentData = null

      for (const line of lines) {
        const trimmedLine = line.trim()
        
        if (trimmedLine === '') {
          // 空行表示一个完整的SSE事件结束
          if (currentData) {
            console.log('完整SSE事件:', { event: currentEvent, data: currentData })
            this.processSSEEvent(currentEvent, currentData, messageObj)
          }
          currentEvent = null
          currentData = null
          continue
        }

        console.log('处理SSE行:', trimmedLine)

        // 解析SSE格式
        if (trimmedLine.startsWith('event: ')) {
          currentEvent = trimmedLine.slice(7) // 移除 'event: '
          console.log('SSE事件类型:', currentEvent)
        } else if (trimmedLine.startsWith('data: ')) {
          currentData = trimmedLine.slice(6) // 移除 'data: '
          console.log('SSE数据:', currentData)
        }
      }

      // 处理缓冲区末尾可能的不完整事件
      if (currentData) {
        console.log('处理缓冲区末尾事件:', { event: currentEvent, data: currentData })
        this.processSSEEvent(currentEvent, currentData, messageObj)
      }
    },

    // 处理单个SSE事件
    processSSEEvent(eventType, data, messageObj) {
      if (data === '[DONE]') {
        console.log('SSE流结束')
        messageObj.status = 'completed'
        this.isStreaming = false
        this.currentReader = null
        return
      }

      try {
        const eventData = JSON.parse(data)
        console.log('接收到SSE事件:', eventData)
        const handleResult = this.handleSSEEvent(eventData, messageObj)
        console.log('handleSSEEvent返回结果:', handleResult)

        // 如果需要创建新消息，则创建并添加到历史记录中
        if (handleResult && handleResult.needNewMessage) {
          const newMessage = {
            id: Date.now() + '_' + handleResult.type + '_' + Math.random().toString(36).substring(2, 8),
            query: '',
            answer: handleResult.content,
            status: handleResult.status || 'completed',
            created_at: new Date().toISOString(),
            messageType: handleResult.type, // 添加消息类型标识
            toolName: handleResult.toolName // 添加工具名标识（如果有的话）
          }
          this.historyMessages.push(newMessage)
          console.log('创建新消息:', newMessage)
          console.log('当前消息总数:', this.historyMessages.length)
          this.debugMessages()
        } else if (handleResult && handleResult.needNewMessage === false) {
          // 如果不需要创建新消息，则将内容追加到当前消息中
          messageObj.answer += '\n\n' + handleResult.content
          console.log('追加到现有消息:', handleResult.content)
        }
      } catch (e) {
        console.warn('解析SSE数据失败:', {
          原始数据: data,
          错误: e.message,
          错误详情: e
        })
        // 如果不是JSON，可能是纯文本，直接追加
        if (data && data !== '[DONE]') {
          console.log('作为纯文本处理:', data)
          messageObj.answer += data
        }
      }
    },

    // 处理SSE事件
    handleSSEEvent(eventData, messageObj) {
      // 根据事件类型处理不同的数据
      if (eventData.type === 'agent_start') {
        // 智能体开始响应
        messageObj.status = 'streaming'
      } else if (eventData.type === 'thinking') {
        // 思考过程，创建新的消息对象单独展示
        if (eventData.message) {
          return {
            needNewMessage: true,
            type: 'thinking',
            content: `**思考中** ${eventData.message}`,
            status: 'completed'
          }
        }
      } else if (eventData.type === 'decision') {
        // 决策信息，创建新的消息对象单独展示
        if (eventData.content) {
          return {
            needNewMessage: true,
            type: 'decision',
            content: `**决策** \n\n${eventData.content}`,
            status: 'completed'
          }
        }
      } else if (eventData.type === 'tool_start') {
        console.log('tool_start', eventData)
        // 工具开始执行，创建新的消息对象单独展示
        // const toolName = eventData.tool_name || '工具'
        // const params = eventData.parameters ? `\n\n**参数:**\n\`\`\`json\n${JSON.stringify(eventData.parameters, null, 2)}\n\`\`\`` : ''
        // return {
        //   needNewMessage: true,
        //   type: 'tool_start',
        //   content: `**执行开始** \n\n**工具:** ${toolName}${params}`,
        //   status: 'completed'
        // }
      } else if (eventData.type === 'tool_result') {
        // 工具执行结果，创建新的消息对象单独展示
        const toolName = eventData.tool_name || '工具'
        const result = eventData.result || eventData.content || ''
        return {
          needNewMessage: true,
          type: 'tool_result',
          content: `**执行结果** ${result}`,
          status: 'completed'
        }
      } else if (eventData.type === 'tool_streaming') {
        // 工具流式输出，更新或创建流式消息
        const toolName = eventData.tool_name || '工具'
        const content = eventData.content || eventData.accumulated || ''

        // 查找当前对话轮次中是否已存在该工具的流式消息
        // 通过查找最后一个用户消息之后的消息来限定当前轮次
        const lastUserMessageIndex =
          this.historyMessages
            .map((msg, index) => ({ msg, index }))
            .filter(item => item.msg.query && !item.msg.answer)
            .pop()?.index || -1

        const currentRoundMessages = this.historyMessages.slice(lastUserMessageIndex + 1)
        const existingStreamingMessage = currentRoundMessages.find(msg => msg.messageType === 'tool_streaming' && msg.toolName === toolName && msg.status === 'streaming')

        if (existingStreamingMessage) {
          // 更新现有流式消息 - 使用累积的内容
          if (eventData.accumulated) {
            // 如果有累积内容，直接使用累积内容
            existingStreamingMessage.answer = `**输出:**\n${eventData.accumulated}`
          } else if (content) {
            // 如果只有增量内容，追加到现有内容
            const currentOutput = existingStreamingMessage.answer.split('**输出:**\n')[1] || ''
            existingStreamingMessage.answer = `**输出:**\n${currentOutput}${content}`
          }
          console.log('更新现有流式消息:', existingStreamingMessage)
        } else {
          // 创建新的流式消息
          console.log('创建新的tool_streaming消息:', content)
          return {
            needNewMessage: true,
            type: 'tool_streaming',
            content: `**输出:**\n${content}`,
            status: 'streaming',
            toolName: toolName // 添加工具名标识
          }
        }
      } else if (eventData.type === 'status' && eventData.message !== '[生成任务摘要]') {
        // 状态更新，创建新的消息对象单独展示
        if (eventData.message) {
          return {
            needNewMessage: true,
            type: 'status',
            content: `${eventData.message}`,
            status: 'completed'
          }
        }
      } else if (eventData.type === 'summary') {
        // 摘要信息处理
        if (eventData.content) {
          this.showSummary = true

          // 处理内容：如果是对象，则转换为格式化的JSON字符串；如果是字符串，直接使用
          let contentToAdd = ''
          if (typeof eventData.content === 'object') {
            // 如果是对象，转换为格式化的JSON字符串
            contentToAdd = '```json\n' + JSON.stringify(eventData.content, null, 2) + '\n```'
          } else {
            // 如果是字符串，直接使用
            contentToAdd = eventData.content
          }

          // 累加摘要内容
          this.summaryContent += contentToAdd

          // 如果是流式内容，设置流式状态
          if (eventData.streaming) {
            this.isSummaryStreaming = true
          } else {
            // 非流式或结束时
            this.isSummaryStreaming = false
          }
          // 不在消息流中显示摘要
          return null
        }

        // 如果有用户ID，可以记录
        if (eventData.user_id) {
          console.log('用户ID:', eventData.user_id)
        }
      } else if (eventData.type === 'error') {
        return {
          needNewMessage: true,
          type: 'error',
          content: `**执行异常** \n\n${eventData.message}`,
          status: 'completed'
        }
      } else if (eventData.type === 'close') {
        console.log('close', eventData)
      } else if (eventData.content) {
        // 通用内容处理 - 这是最终的回答内容
        messageObj.answer += eventData.content
      } else if (typeof eventData === 'string') {
        // 纯文本内容 - 这是最终的回答内容
        messageObj.answer += eventData
      }

      // 默认不需要创建新消息
      return null
    },

    // 停止输出
    stopOutput(messageObj) {
      if (this.currentReader) {
        try {
          this.currentReader.cancel()
        } catch (e) {
          console.warn('停止输出失败:', e)
        }
        this.currentReader = null
      }

      if (messageObj) {
        messageObj.status = 'stopped'
        messageObj.answer += '\n\n*【输出已停止】*'
      }

      this.isStreaming = false
      this.isSummaryStreaming = false
    },

    // 停止流式输出并调用后端停止接口
    async stopStreaming() {
      try {
        // 调用后端停止接口
        let res = await agentApi.stopSession({
          session_id: this.currentConversationId,
          user_id: this.name,
          reason: '用户主动停止'
        })
        console.log(res)
        if(res && res.success){
          console.log('停止请求发送成功')
        }else{
          console.warn('停止请求失败:', res.message)
        }
      } catch (error) {
        console.error('发送停止请求失败:', error)
      }

      // 更新最后一条智能体消息的状态
      const lastAgentMessage = this.historyMessages
        .slice()
        .reverse()
        .find(msg => msg.answer && !msg.query)
      if (lastAgentMessage) {
        lastAgentMessage.status = 'stopped'
        lastAgentMessage.answer += '\n\n*【输出已停止】*'
      }

      this.isStreaming = false
      this.isSummaryStreaming = false
    },

    //添加到底部
    scrollToBottom() {
      const messagesContainer = document.querySelector('.chat-messages')
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }
    },

    processDeepThinking(content) {
      // 处理深度思考内容，可以在这里做一些格式化
      return content
    },

    //获取历史对话消息内容
    clickHistoryConversation(item) {
      // 点击历史对话的实现
      this.currentConversationId = item.session_id
      console.log('切换到对话:', item.id)
      // 重置摘要，因为切换到了新的对话
      this.resetSummary()
    },

    showImagePreview(imageUrl) {
      // 显示图片预览
      this.currentPreviewImage = imageUrl
      this.imagePreviewVisible = true
    },

    copyToClipboard(text, type) {
      // 复制到剪贴板
      navigator.clipboard
        .writeText(text)
        .then(() => {
          console.log('已复制到剪贴板')
        })
        .catch(err => {
          console.error('复制失败:', err)
        })
    },

    removeUploadedImage(index) {
      // 删除上传的图片
      this.uploadFile.splice(index, 1)
    },

    triggerFileInput() {
      // 触发文件选择
      if (this.uploadFile.length < 3) {
        this.$refs.fileInput.click()
      }
    },

    // 触发文档上传
    triggerDocumentInput() {
      this.$refs.documentInput.click()
    },

    // 处理文档选择
    async handleDocumentSelected(event) {
      const file = event.target.files[0]
      if (!file) return

      // 检查文件类型
      const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
      if (!this.supportedFileTypes.includes(fileExtension)) {
        alert(`不支持的文件类型: ${fileExtension}`)
        return
      }

      // 检查文件大小（10MB限制）
      if (file.size > 50 * 1024 * 1024) {
        alert('文件大小不能超过5MB')
        return
      }

      this.uploadingDocument = true

      try {
        // 上传并解析文件
        const result = await agentApi.uploadFile(file)
        
        if (result.success) {
          // 解析成功，将内容填入输入框
          const fileContent = `[文件：${result.filename}]\n\n${result.content}`
          
          // 如果输入框已有内容，则追加
          if (this.inputMessage.trim()) {
            this.inputMessage += '\n\n' + fileContent
          } else {
            this.inputMessage = fileContent
          }
          
          // 提示用户
          console.log(`文件 ${result.filename} 解析成功`)
          
        } else {
          // 解析失败
          alert(`文件解析失败: ${result.message}`)
        }
      } catch (error) {
        console.error('文件上传失败:', error)
        alert('文件上传失败，请重试')
      } finally {
        this.uploadingDocument = false
        // 清空input，允许重复选择同一文件
        event.target.value = ''
      }
    },

    handleFileSelected(event) {
      // 处理文件选择
      const files = Array.from(event.target.files)
      files.forEach(file => {
        if (this.uploadFile.length < 3) {
          const fileObj = {
            id: Date.now() + Math.random(),
            name: file.name,
            preview: URL.createObjectURL(file),
            file: file
          }
          this.uploadFile.push(fileObj)
        }
      })
      // 清空input，允许重复选择同一文件
      event.target.value = ''
    },

    handleEnterPress(event) {
      // 处理回车键
      if (!this.isComposing && !event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },

    handlePaste(event) {
      // 处理粘贴事件，支持图片粘贴
      const items = event.clipboardData.items
      for (let item of items) {
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile()
          if (file && this.uploadFile.length < 3) {
            const fileObj = {
              id: Date.now() + Math.random(),
              name: `粘贴图片_${Date.now()}.png`,
              preview: URL.createObjectURL(file),
              file: file
            }
            this.uploadFile.push(fileObj)
          }
        }
      }
    },

    formatTimestamp(timestamp) {
      // 格式化时间戳
      const date = new Date(timestamp)
      return date.toLocaleString()
    },

    // 获取流式文本
    getStreamingText(messageType) {
      const textMap = {
        thinking: '正在思考',
        decision: '正在决策',
        tool_start: '启动中',
        tool_result: '执行中',
        tool_streaming: '智能体输出',
        status: '状态更新中',
        summary: '生成摘要中'
      }
      return textMap[messageType] || 'AI正在思考回复...'
    },

    /**
     * 重置摘要数据
     */
    resetSummary() {
      this.summaryContent = ''
      this.showSummary = false
      this.isSummaryStreaming = false
    },

    /**
     * 调试方法：打印当前消息状态
     */
    debugMessages() {
      console.log('=== 消息调试信息 ===')
      console.log('消息总数:', this.historyMessages.length)
      console.log('消息列表:', this.historyMessages)
      this.historyMessages.forEach((msg, index) => {
        console.log(`消息${index}:`, {
          id: msg.id,
          query: msg.query,
          answer: msg.answer,
          status: msg.status,
          messageType: msg.messageType,
          hasAnswer: !!msg.answer,
          hasMessageType: !!msg.messageType
        })
      })
    },

    // 处理初始消息的方法
    handleInitialMessage() {
      // 从setup中返回的chatStore访问
      if (this.chatStore) {
        const initialMessage = this.chatStore.getAndClearInitialMessage()
        if (initialMessage && initialMessage.trim()) {
          console.log('获取到初始消息:', initialMessage)
          // 设置到输入框
          this.inputMessage = initialMessage
          // 等待组件完全挂载后自动发送
          this.$nextTick(() => {
            console.log('自动发送初始消息:', initialMessage)
            this.sendMessage()
          })
        }
      }
    }
  },

  setup() {
    const appStore = useAppStore()
    const chatStore = useChatStore()
    return {
      appStore,
      chatStore
    }
  },

  created() {
    // 使用 Pinia store 获取用户名
    this.name = this.appStore.user.name || 'default_user'

    if (!this.$route.query.agentId) {
      this.$router.push({
        path: '/agent'
      })
      return
    }

    this.currentAgentId = this.$route.query.agentId

    // 初始化会话ID
    this.currentConversationId = 'session_' + Date.now()
  },

  mounted() {
    this.name = 'default_user'
    // 获取智能体列表
    this.getAgentList()
    //获取历史会话
    this.getHistoryConversationsList()
    // 获取支持的文件类型
    this.getSupportedFileTypes()
    
    // 调试信息
    console.log('组件mounted，初始消息数量:', this.historyMessages.length)
    
    // 添加全局调试方法
    window.debugMessages = this.debugMessages

    // 从store获取初始消息并自动发送
    this.handleInitialMessage()
  }
}
</script>

<style scoped>
@import '@/assets/css/style.css';
/* 确保CSS变量正确继承 */

/* ElementUI组件统一样式覆盖 - 放在最前面确保优先级 */
.params-section ::v-deep .el-form,
.params-section ::v-deep .el-form-item,
.params-section ::v-deep .el-form-item__label,
.params-section ::v-deep .el-input,
.params-section ::v-deep .el-input__inner,
.params-section ::v-deep .el-textarea__inner,
.params-section ::v-deep .el-select,
.params-section ::v-deep .el-button,
.params-section ::v-deep .el-select-dropdown__item,
.params-section ::v-deep .el-select-dropdown,
.params-section ::v-deep .el-select-dropdown__item,
.params-section ::v-deep .el-popper,
.params-section ::v-deep .el-tooltip__popper,
.params-section ::v-deep .el-message-box,
.params-section ::v-deep .el-dropdown-menu,
.params-section ::v-deep .el-radio,
.params-section ::v-deep .el-checkbox,
.params-section ::v-deep .el-tag,
.params-section ::v-deep .el-date-picker,
.params-section ::v-deep .el-table {
  font-family:
    'Inter',
    'SF Pro Display',
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-size: 13px;
}
/* 对话记录样式 */

.message.agent {
  margin-bottom: 0 !important;
}

.chat-session {
  position: relative;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  background-color: var(--card-bg-color);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.chat-session:hover {
  background-color: var(--hover-color);
}

.chat-session.active {
  background-color: var(--active-color);
}

.chat-session-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 24px;
}

.chat-session-time {
  font-size: 12px;
  color: var(--text-light-color);
}

.chat-session-actions {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
}

.chat-session-more {
  position: relative;
  padding: 4px;
  color: var(--text-light-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.chat-session-more:hover {
  color: var(--primary-color);
}

.chat-session-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--card-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 4px 0;
  min-width: 120px;
  z-index: 1999;
  display: none;
}

.chat-session-menu.active {
  display: block;
}

.menu-item {
  padding: 8px 16px;
  font-size: 14px;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.menu-item:hover {
  background-color: var(--hover-color);
  color: var(--primary-color);
}

.menu-item i {
  font-size: 16px;
}

/* 保留其他非 Markdown 相关的样式 */
.chat-sections-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  padding-bottom: 0;
}

.chat-section-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.history-section {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: #ffffff;
}

.agent-section {
  flex-shrink: 0;
  border-top: 1px solid var(--border-color);
  padding-top: 0.5rem;
  background-color: var(--card-bg-color);
  z-index: 5;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.scrollable {
  overflow-y: auto;
  flex: 1;
}

.chat-section#history {
  height: 100%;
  overflow-y: auto;
}

.chat-section#agents {
  height: 100%;
  overflow-y: auto;
}

.chat-section#history::-webkit-scrollbar {
  width: 6px;
}

.chat-section#history::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

.chat-section#history::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.chat-section#history::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.15);
}

.chat-section#agents::-webkit-scrollbar {
  width: 6px;
}

.chat-section#agents::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

.chat-section#agents::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.chat-section#agents::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.15);
}

.chat-main {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
}

.chat-input-container {
  flex-shrink: 0;
  position: sticky;
  bottom: 0rem;
  display: block;
  max-width: 900px;
  margin: 0 auto;
  width: 100%;
  border-radius: 16px;
  background-color: var(--card-bg-color);
}

@media (max-width: 1200px) {
  .chat-input-container {
    max-width: 800px;
  }
}

@media (max-width: 992px) {
  .chat-input-container {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .chat-sidebar.mobile.active .chat-sections-container {
    height: calc(100vh - var(--header-height) - 110px);
  }

  .chat-sidebar.mobile.active .scrollable {
    max-height: calc(100vh - 400px);
  }

  .chat-sidebar.mobile.active .agent-section {
    bottom: 0;
  }
}

.chat-input-actions-container {
  position: absolute;
  right: 9rem;
  z-index: 2;
}

.chat-input-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.chat-input-actions .btn-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  padding: 0;
  color: #64748b;
  background-color: transparent;
  border-radius: 50%;
  transition: all 0.2s;
}

.chat-input-actions .btn-icon:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--primary-color);
}

.isStreaming .chat-input {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

.zp-agent {
  background-color: #f5f7fa;
  min-height: calc(100vh - var(--header-height) - 0px);
}

.message.agent .message-content.streaming::after {
  /* content: '●'; */
  display: inline-block;
  color: #0366d6;
  margin-left: 5px;
  animation: blinkDots 1.5s infinite;
  font-size: 8px;
  vertical-align: middle;
}

@keyframes blinkDots {
  0%,
  100% {
    transform: translateY(0);
    opacity: 0.2;
  }
  50% {
    transform: translateY(-5px);
    opacity: 1;
  }
}

.message.agent[data-status='streaming'] .message-actions,
.message.agent[data-status='streaming'] .message-reactions {
  opacity: 1;
  pointer-events: auto;
}

.message.agent[data-status='streaming'] .message-action-btn {
  opacity: 1;
  pointer-events: auto;
}

.message.agent[data-status='streaming'] .stop-output-btn-card {
  opacity: 1;
  pointer-events: auto;
}

.message-content-user-query {
  margin: 0;
  font-family: inherit;
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 14.5px;
  color: #ffffff;
  background-color: transparent;
  border: none;
  padding: 3px;
}

.btn-send:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-send.btn-stop {
  background-color: #ff4757;
  border-color: #ff4757;
}

.btn-send.btn-stop:hover {
  background-color: #ff3742;
  border-color: #ff3742;
}

.message.agent {
  position: relative;
  margin-bottom: 30px;
}

.message.agent.modern-markdown:first-child {
  margin-bottom: 30px;
}

.message.user {
  margin-right: 8px;
}

.message.user .message-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message.user:hover .message-actions {
  opacity: 1;
}

.message.agent {
  margin-left: 8px;
}

.message-content-wrapper {
  position: relative;
  flex: 1;
}

.rename-input {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid var(--primary-color);
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  background-color: white;
}

.rename-input:focus {
  box-shadow: 0 0 0 2px rgba(106, 17, 203, 0.2);
}

/* 添加系统消息卡片的样式 */
.message.system {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
  padding: 0 1rem;
}

.message.system .message-content {
  max-width: 800px;
  width: 100%;
  background-color: rgba(var(--primary-color-rgb), 0.05);
  /* padding: 12px 16px; */
  text-align: center;
}

@media (max-width: 992px) {
  .message.system .message-content {
    max-width: 100%;
  }
}

/* 新增参数侧边栏样式 */
.params-sidebar {
  position: fixed;
  top: 60px;
  right: 0;
  width: 320px;
  height: calc(100vh - 60px);
  background-color: var(--card-bg-color);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
}

.params-sidebar.active {
  transform: translateX(0);
}

.params-sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.params-sidebar-title {
  font-size: 16px;
  font-weight: 600;
}

.params-sidebar-content {
  padding: 16px;
}

.params-form .form-group {
  margin-bottom: 30px;
}

.params-form .btn-block {
  width: 100%;
  margin-bottom: 16px;
}

.custom-param {
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  margin-bottom: 10px;
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.custom-param .form-control {
  margin-bottom: 8px;
}

.overlay.active {
  display: block;
  opacity: 1;
  visibility: visible;
}

.params-toggle-btn {
  position: fixed;
  right: 20px;
  bottom: 90px;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  padding: 8px 16px;
  border-radius: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 99;
  transition: all 0.2s ease;
}

.params-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.params-toggle-btn i {
  margin-right: 6px;
  font-size: 16px;
}

.params-toggle-btn span {
  font-size: 14px;
  font-weight: 500;
}

/* 适配 active 状态 */
.params-sidebar {
  transform: translateX(100%);
}

.params-sidebar.active {
  transform: translateX(0);
}

/* 当侧边栏激活时的样式调整 */
.chat-container {
  position: relative;
  transition: padding-right 0.3s ease;
}

.chat-container.params-active {
  padding-right: 320px;
}

@media (max-width: 768px) {
  .params-sidebar {
    width: 290px;
  }

  .chat-container.params-active {
    padding-right: 0;
  }
}

/* 布局调整 */
.chat-container {
  display: flex;
  height: calc(100vh - var(--header-height));
}

.chat-sidebar {
  width: 260px;
  border-right: 1px solid var(--border-color);
  background-color: var(--sidebar-bg-color);
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.chat-main {
  flex: 1;
  position: relative;
  height: 100%;
  overflow: hidden;
}

/* 新增参数区域样式 */
.params-section {
  background-color: var(--card-bg-color);
  height: 100%;
  width: 450px;
  border-left: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  overflow: hidden;
  transition: width 0.3s ease;
}

.params-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.params-title {
  height: 24px;
  font-size: 16px;
  font-weight: 600;
}

.params-content {
  width: 450px;
  padding: 0 16px 16px 16px;
  height: 100%;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .chat-container {
    flex-direction: column;
  }

  .chat-sidebar,
  .chat-main,
  .params-section {
    width: 100%;
    height: auto;
  }

  .params-section {
    border-left: none;
    border-top: 1px solid var(--border-color);
  }

  .params-content {
    width: 100%;
  }

  .chat-main {
    flex: 1;
    min-height: 50vh;
  }
}

.chat-session-menu:hover {
  background-color: var(--hover-color);
  color: var(--primary-color);
}

/* 推荐问题样式 */
.suggested-questions {
  margin-top: 5px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-width: 600px;
}

.suggested-question {
  display: inline-block;
  padding: 8px 12px;
  background-color: #fff;
  color: #333;
  font-size: 13px;
  font-weight: 400;
  cursor: pointer;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  white-space: nowrap;
  max-width: 100%;
}

.suggested-question:hover {
  background-color: #f5f7fa;
  color: #0366d6;
  border-color: #0366d6;
}

/* 添加消息操作布局样式 */
.message-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 5px;
}

.message-actions-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.message-actions-right {
  display: flex;
  gap: 8px;
  margin-left: 10px;
}

/* 聊天输入操作样式 */
.chat-input-actions-left {
  width: 60px;
  flex: 1;
  /* display: contents; */
}

.chat-input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@keyframes typingCursor {
  0%,
  50% {
    opacity: 1;
    transform: scaleY(1);
  }
  51%,
  100% {
    opacity: 0.3;
    transform: scaleY(0.8);
  }
}

/* 流式输出容器样式 */
.message-content.streaming {
  position: relative;
  overflow: hidden;
}

/* 超时和错误状态 */
.message.agent[data-status='error'] .message-content {
  color: #ff4d4f;
}

/* 流式状态指示器 */
.streaming-indicator {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: rgba(66, 153, 225, 0.1);
  border-radius: 6px;
  border-left: 3px solid #4299e1;
  font-size: 13px;
  color: #4299e1;
  overflow: hidden;
}

.streaming-dots {
  display: flex;
  gap: 4px;
}

.streaming-dots .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #4299e1;
  animation: streamingDots 1.4s infinite ease-in-out;
}

.streaming-dots .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.streaming-dots .dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes streamingDots {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.streaming-text {
  font-weight: 500;
  font-size: 12px;
}

/* 流式进度条 */
.streaming-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(66, 153, 225, 0.2);
  overflow: hidden;
}

.streaming-progress-bar {
  width: 100%;
  height: 100%;
  background: var(--primary-gradient);
}

/* 流式激活状态 */
.message-content-agent.streaming-active {
  padding-left: 12px;
  transition: all 0.3s ease;
}

/* 流式完成状态 */
.message-content-agent:not(.streaming) {
  border-left: 3px solid transparent;
  transition: all 0.3s ease;
}

/* 添加引用资源样式 */
.retriever-resources {
  margin-top: 10px;
  padding: 10px;
  /* border-top: 1px solid #eaeaea; */
}

.retriever-resources-title {
  font-size: 13px;
  font-weight: 500;
  color: #666;
  margin-bottom: 10px;
  display: flex;
}

.retriever-resources-title-icon {
  margin: 8px 5px;
  flex-grow: 1;
  background-color: rgb(234, 235, 237);
  height: 1px;
}

.retriever-resources-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.retriever-resource-item {
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 0.5rem;
  font-size: 12px;
  color: #0366d6;
  border: 1px solid #e1e4e8;
}

/* 图片上传相关样式 */
.image-previews-wrapper {
  /* background-color: #f8f9fa; */
  border-radius: 8px;
  margin-top: 10px;
}

.image-previews-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 10px;
}

.image-count {
  font-size: 12px;
  color: #666;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 8px;
  border-radius: 10px;
}

.image-previews-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 0 10px;
}

.image-preview-item {
  position: relative;
  width: 60px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e1e4e8;
  background-color: #f6f8fa;
  margin-bottom: 5px;
}

.image-preview-item.uploading {
  border: 1px dashed #1e88e5;
  background-color: rgba(30, 136, 229, 0.1);
}

.image-preview-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 10px;
  padding: 3px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.image-preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}

.loading-icon {
  font-size: 24px;
  margin-bottom: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.upload-text {
  font-size: 14px;
}

.image-preview-actions {
  position: absolute;
  top: 2px;
  right: 2px;
  z-index: 10;
}

.image-action-btn {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.image-action-btn:hover {
  background-color: rgba(231, 76, 60, 0.8);
}

.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
}

.btn-disabled:hover {
  background-color: transparent !important;
  color: #64748b !important;
}

.user-message-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.user-message-image-item {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e1e4e8;
  background-color: #f6f8fa;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.user-message-image-item:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-message-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.preview-dialog-img {
  max-width: 100%;
  max-height: 80vh;
  display: block;
  margin: 0 auto;
}

/* 要让对话框内容居中 */
::v-deep .el-dialog__body {
  text-align: center;
  padding: 10px;
}

/* 特殊消息类型样式 */
.message-special {
  opacity: 0.95;
}

/* 摘要样式 */
.message-type-summary {
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
  border-left: 3px solid #ec4899;
}

/* 特殊消息的头像样式 */
.message-special .message-avatar {
  width: 28px;
  height: 28px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 特殊消息的字体样式 */
.message-special .message-content {
  font-size: 13px;
  line-height: 1.6;
}

/* 调整特殊消息的间距 */
.message-special {
  margin-bottom: 12px;
}

.message-special + .message:not(.message-special) {
  margin-top: 8px;
}

.speck-icon{
  margin-right:5px;
}

/* 文件上传加载动画 */
.loading-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
