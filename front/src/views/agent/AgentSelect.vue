<template>
  <AppLayout>
    <div class="agent-select">
      <!-- 主要内容区域 -->
      <div class="main-container">
        <!-- 欢迎信息 -->
        <div class="welcome-section">
          <h1 class="welcome-title">你好，我是AI智能体～</h1>
          <p class="welcome-subtitle">发现并使用各种强大的AI智能体，一键开始任务</p>
          
          <!-- 输入区域 -->
          <div class="input-section">
            <div class="input-container">
              <textarea
                v-model="userInput"
                placeholder="请选择智能体，进行开聊～"
                class="user-input"
              ></textarea>
              <button 
                class="send-btn"
                :disabled="!userInput.trim()"
                @click="startChat"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- 智能体选择区域 -->
        <div class="agent-section">
          <h3 class="section-title">请选择Agent</h3>
          <div class="agents-container">
            <div 
              v-for="agent in agents" 
              :key="agent.id"
              :class="['agent-card', { active: selectedAgent?.id === agent.id }]"
              @click="selectAgent(agent)"
            >
              <div class="agent-icon">
                <i :class="agent.icon"></i>
              </div>
              <div class="agent-name">{{ agent.name }}</div>
              <div class="agent-stats">
                <i class="ri-fire-line" style="color: var(--primary-color);"></i>
                <span style="color: var(--primary-color);">{{ agent.usage }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useChatStore } from '@/store'
import { ElMessage } from 'element-plus'

import AppLayout from '@/components/AppLayout.vue'

import { agentApi } from '@/api/index.js'

export default {
  name: 'AgentSelect',
  components: {
    AppLayout
  },
  setup() {
    const router = useRouter()
    const chatStore = useChatStore()
    
    // 响应式数据
    const userInput = ref('')
    const selectedAgent = ref(null)
    
    // 智能体数据 - 初始化为空数组，将从API获取
    const agents = ref([])
    
    // 默认智能体数据（作为备用）
    const defaultAgents = [
      {
        id: 'test-case-v7',
        name: '测试用例编写V7',
        icon: 'ri-test-tube-line',
        usage: 151,
        description: '专业的测试用例生成和编写'
      },
      {
        id: 'test-review',
        name: '测试点挖掘',
        icon: 'ri-search-line',
        usage: 20,
        description: '深度挖掘测试点和边界条件'
      },
      {
        id: 'requirements-analysis-v2',
        name: '需求分析V2',
        icon: 'ri-file-list-3-line',
        usage: 21,
        description: '专业的需求分析和整理'
      }
    ]

    // 方法
    const selectAgent = (agent) => {
      selectedAgent.value = agent
      ElMessage.success(`已选择：${agent.name}`)
    }

    const startChat = () => {      
      if (!userInput.value.trim()) {
        ElMessage.warning('请输入内容')
        return
      }
      
      if (!selectedAgent.value) {
        ElMessage.warning('请先选择智能体')
        return
      }

      console.log('开始保存到store和跳转')
      
      try {
        // 保存选中的智能体和初始消息到store
        chatStore.selectAgent(selectedAgent.value)
        chatStore.setInitialMessage(userInput.value.trim())
        
        console.log('store保存成功，开始跳转')
        
        // 跳转到聊天页面
        router.push({
          path: '/chat',
          query: {
            agentId: selectedAgent.value.id,
            agentName: selectedAgent.value.name
          }
        })
        
        console.log('跳转命令已执行')
      } catch (error) {
        console.error('发生错误:', error)
        ElMessage.error('跳转失败: ' + error.message)
      }
    }

    return {
      userInput,
      selectedAgent,
      agents,
      selectAgent,
      startChat
    }
  },
  methods: {

    // 获取智能体列表
    async getAgentList() {
      try {
        let res = await agentApi.getAgents()
        console.log('获取智能体列表:', res)
        // 如果API返回数据，可以更新agents数组
        if (res && res.agents) {
          this.agents = res.agents
          this.agents.forEach((agent, index) => {
            if(index == 0){
              agent.icon = 'ri-speak-ai-fill'
            }else if(index == 1){
              agent.icon = 'ri-test-tube-line'
            }else if(index == 2){
              agent.icon = 'ri-file-list-3-line'
            }else if(index == 3){
              agent.icon = 'ri-search-line'
            }
            agent.usage = Math.floor(Math.random() * 100)
          })
        }
      } catch (error) {
        console.error('获取智能体列表失败:', error)
      }
    }
  },

  mounted() {
    this.getAgentList()
  }


}
</script>

<style scoped>
.agent-select {
  min-height: 100vh;
  background: #fafbfc;
}

.main-container {
  min-height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: 50px;
  width: 100%;
}

.welcome-title {
  font-size: 40px;
  font-weight: 500;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.welcome-subtitle {
  font-size: 18px;
  color: #666666;
  margin: 0 0 60px 0;
  line-height: 1.5;
}

.input-section {
  /* max-width: 800px; */
  margin: 0 auto;
  width: 100%;
}

.input-container {
  position: relative;
  background: white;
  border-radius: 12px;
  padding: 10px 60px 10px 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.input-container:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  border-color: #d0d0d0;
}

.user-input {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  resize: none;
  min-height: 90px;
  font-family: inherit;
}

.user-input::placeholder {
  color: #999;
  font-size: 16px;
}



.send-btn {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #007AFF;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
}

.send-btn:hover:not(:disabled) {
  background: #0056CC;
  transform: translateY(-50%) scale(1.05);
}

.send-btn:disabled {
  background: #e5e7eb;
  cursor: not-allowed;
  color: #9ca3af;
}

.agent-section {
  width: 100%;
  text-align: left;
}

.section-title {
  font-size: 18px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 24px;
}

.agents-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.agent-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 8px 10px;
  border: 1px solid #f6f6f6;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 200px;
  position: relative;
  color: red;
}

.agent-card:hover {
  border-color: #007AFF;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.1);
  transform: translateY(-1px);
}

.agent-card.active {
  border-color: #007AFF;
  background: #f0f8ff;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
}

.agent-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
  color: white;
}

/* 不同智能体的颜色 */
.agent-card:nth-child(1) .agent-icon {
  background: #5865f2; /* 紫色 */
}

.agent-card:nth-child(2) .agent-icon {
  background: #00bcd4; /* 青色 */
}

.agent-card:nth-child(3) .agent-icon {
  background: #9c27b0; /* 紫红色 */
}

.agent-card:nth-child(4) .agent-icon {
  background: #2196f3; /* 蓝色 */
}

.agent-name {
  font-size: 15px;
  font-weight: 500;
  color: var(--primary-color);
  flex: 1;
}

.agent-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #666666;
}

.agent-stats svg {
  color: #999;
}

@media (max-width: 768px) {
  .main-container {
    padding: 20px 16px;
  }
  
  .welcome-title {
    font-size: 28px;
  }
  
  .welcome-subtitle {
    font-size: 16px;
    margin-bottom: 40px;
  }
  
  .welcome-section {
    margin-bottom: 60px;
  }
  
  .agents-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .agent-card {
    min-width: auto;
    width: 100%;
  }
  
  .input-container {
    padding: 14px 50px 14px 16px;
  }
}

@media (max-width: 480px) {
  .welcome-title {
    font-size: 24px;
  }
  
  .agent-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }
  
  .agent-name {
    font-size: 14px;
  }
}
</style> 