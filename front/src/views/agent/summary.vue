<template>
  <div class="summary-section">
    <!-- <div class="summary-header">
      <div class="summary-title">
        <i class="ri-file-list-3-line" style="margin-right: 6px"></i>
        总结概要
      </div>
      <div v-if="isStreaming" class="summary-streaming-indicator">
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
      </div>
    </div> -->
    <div class="summary-body">
      <!-- 使用与注册时相同的组件名 -->
      <MarkdownRenderer :content="summaryContent" messageType="agent" :is-streaming="isStreaming"></MarkdownRenderer>
    </div>
  </div>
</template>

<script>
import MarkdownRenderer from '@/components/markdownIt/MarkdownRendererAgent.vue'
export default {
  name: 'summary',
  components: {
    MarkdownRenderer
  },
  props: {
    summaryContent: {
      type: String,
      default: ''
    },
    isStreaming: {
      type: <PERSON>olean,
      default: false
    }
  },

  watch: {},

  methods: {}
}
</script>

<style scoped>
.summary-section {
  /* padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  position: relative; */
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.summary-streaming-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.summary-streaming-indicator .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #0366d6;
  animation: streamingDots 1.4s infinite ease-in-out;
}

.summary-streaming-indicator .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.summary-streaming-indicator .dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes streamingDots {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.summary-body {
  font-size: 14px;
  line-height: 1.6;
  color: #555;
}

/* 过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>
