<template>
  <AppLayout>
    <div class="home">
      <!-- 主要内容 -->
      <el-main class="main-content">
      <!-- 欢迎区域 -->
      <div class="welcome-section">
        <div class="welcome-content">
          <h1 class="welcome-title">欢迎使用AI智能体测试平台</h1>
          <p class="welcome-subtitle">AutoGen v0.6和ReAct循环的智能体测试平台</p>
          <div class="welcome-actions">
            <el-button type="primary" size="large" @click="goToChat">
              <i class="ri-chat-3-line"></i>
              开始对话
            </el-button>
            <el-button size="large" @click="learnMore">
              <i class="ri-information-line"></i>
              了解更多
            </el-button>
          </div>
        </div>
      </div>

      <!-- 功能特性 -->
      <div class="features-section">
        <h2 class="section-title">核心功能</h2>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="ri-links-line"></i>
              </div>
              <h3>多智能体协作</h3>
              <p>支持多个智能体团队协作，实现复杂任务的分工处理</p>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="ri-flashlight-line"></i>
              </div>
              <h3>实时流式输出</h3>
              <p>基于SSE技术，提供实时的智能体思考和执行过程展示</p>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="ri-chat-4-line"></i>
              </div>
              <h3>会话管理</h3>
              <p>完整的会话生命周期管理，支持多用户会话隔离</p>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="ri-settings-3-line"></i>
              </div>
              <h3>可扩展架构</h3>
              <p>基于AutoGen框架，支持灵活的智能体扩展</p>
            </div>
          </el-col>
        </el-row>
      </div>


    </el-main>
    </div>
  </AppLayout>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/store/index'

import AppLayout from '@/components/AppLayout.vue'

export default {
  name: 'Home',
  components: {
    AppLayout
  },
  setup() {
    const router = useRouter()
    const appStore = useAppStore()

    const goToChat = () => {
      router.push('/agent')
    }

    const learnMore = () => {
      router.push('/about')
    }

    return {
      goToChat,
      learnMore
    }
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;

}



.main-content {
  padding: 0;
}

.welcome-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 53vh;
  text-align: center;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.welcome-content {
  max-width: 700px;
  padding: 0 20px;
}

.welcome-title {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.welcome-subtitle {
  font-size: 18px;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
}

.welcome-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.welcome-actions .el-button i {
  margin-right: 8px;
  font-size: 16px;
}

.features-section,
.tech-section {
  background: white;
  padding: 80px 20px;
}

.section-title {
  text-align: center;
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 60px;
  color: #2c3e50;
}

.feature-card {
  text-align: center;
  padding: 40px 20px;
  border-radius: 12px;
  background: #f8f9fa;
  height: 100%;
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 20px;
}

.feature-icon i {
  font-size: 48px;
}

.feature-card h3 {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #2c3e50;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

.tech-section {
  background: #f8f9fa;
}

.tech-grid {
  max-width: 800px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.tech-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tech-label {
  font-weight: bold;
  color: #409eff;
  margin-right: 20px;
  min-width: 80px;
}

.tech-value {
  color: #2c3e50;
}

@media (max-width: 768px) {
  .welcome-title {
    font-size: 32px;
  }
  
  .welcome-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .section-title {
    font-size: 28px;
  }
  
  .features-section,
  .tech-section {
    padding: 40px 20px;
  }
}
</style> 