<template>
  <div class="agent-chat">
    <!-- 导航头部 -->
    <div class="chat-header">
      <div class="header-left">
        <el-button text @click="goBack">
          <i class="ri-arrow-left-line"></i>
          返回
        </el-button>
        <h2>智能体对话</h2>
      </div>
      <div class="header-right">
        <el-badge :value="messages.length" :max="99" class="message-badge">
          <i class="ri-chat-3-line"></i>
        </el-badge>
        <el-button 
          :type="isConnected ? 'success' : 'danger'" 
          :icon="isConnected ? 'Check' : 'Close'"
          size="small"
          round
        >
          {{ isConnected ? '已连接' : '未连接' }}
        </el-button>
      </div>
    </div>

    <div class="chat-container">
      <!-- 侧边栏 - 智能体列表 -->
      <div class="chat-sidebar">
        <div class="sidebar-header">
          <h3>可用智能体</h3>
          <el-button size="small" text @click="refreshAgents">
            <i class="ri-refresh-line"></i>
          </el-button>
        </div>
        <div class="agents-list">
          <div 
            v-for="agent in agents" 
            :key="agent.id"
            :class="['agent-item', { active: currentAgent?.id === agent.id }]"
            @click="selectAgent(agent)"
          >
            <div class="agent-avatar">{{ agent.icon }}</div>
            <div class="agent-info">
              <div class="agent-name">{{ agent.name }}</div>
              <div class="agent-desc">{{ agent.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要聊天区域 -->
      <div class="chat-main">
        <!-- 消息区域 -->
        <div class="messages-container" ref="messagesContainer">
          <div v-if="messages.length === 0" class="empty-messages">
            <i class="ri-chat-3-line"></i>
            <p>选择一个智能体开始对话</p>
          </div>
          <div 
            v-for="message in messages"
            :key="message.id"
            :class="['message-item', message.type]"
          >
            <!-- 用户消息 -->
            <div v-if="message.type === 'user'" class="message-bubble user-message">
              <div class="message-content">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>

            <!-- 智能体消息 -->
            <div v-else-if="message.type === 'agent'" class="message-bubble agent-message">
              <div class="agent-avatar">{{ message.agent?.icon || '🤖' }}</div>
              <div class="message-body">
                <div class="agent-name">{{ message.agent?.name || '智能体' }}</div>
                <div class="message-content">{{ message.content }}</div>
                <div class="message-time">{{ formatTime(message.timestamp) }}</div>
              </div>
            </div>

            <!-- 系统消息 -->
            <div v-else-if="message.type === 'system'" class="message-bubble system-message">
              <i class="ri-information-line"></i>
              <span>{{ message.content }}</span>
            </div>

            <!-- 错误消息 -->
            <div v-else-if="message.type === 'error'" class="message-bubble error-message">
              <i class="ri-error-warning-line"></i>
              <span>{{ message.content }}</span>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoading" class="message-item">
            <div class="message-bubble agent-message">
              <div class="agent-avatar">{{ currentAgent?.icon || '🤖' }}</div>
              <div class="message-body">
                <div class="agent-name">{{ currentAgent?.name || '智能体' }}</div>
                <div class="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
          <div class="input-container">
            <el-input
              v-model="inputMessage"
              type="textarea"
              :rows="3"
              placeholder="输入您的问题..."
              resize="none"
              @keydown.ctrl.enter="sendMessage"
            />
            <div class="input-actions">
              <el-button 
                size="small" 
                @click="clearMessages"
                :disabled="messages.length === 0"
              >
                <i class="ri-delete-bin-line"></i>
                清空
              </el-button>
              <el-button 
                type="primary" 
                @click="sendMessage"
                :disabled="!inputMessage.trim() || !currentAgent || isLoading"
              >
                <i class="ri-send-plane-line"></i>
                发送
              </el-button>
            </div>
          </div>
          <div class="input-hint">
            <span>按 Ctrl + Enter 快速发送</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useChatStore, useAppStore } from '@/store'
import { ElMessage } from 'element-plus'

export default {
  name: 'AgentChat',
  setup() {
    const router = useRouter()
    const chatStore = useChatStore()
    const appStore = useAppStore()
    
    // 响应式数据
    const inputMessage = ref('')
    const messagesContainer = ref(null)
    const isLoading = ref(false)
    
    // 计算属性
    const messages = chatStore.getMessages
    const currentAgent = chatStore.getCurrentAgent
    const isConnected = chatStore.getConnectionStatus
    
    // 模拟智能体数据
    const agents = ref([
      {
        id: 'requirements-analysis',
        name: '需求分析师',
        description: '专业的需求分析和整理',
        icon: '📋'
      },
      {
        id: 'test-case-generator',
        name: '测试用例生成器',
        description: '生成完整的测试用例',
        icon: '🧪'
      },
      {
        id: 'interface-analyzer',
        name: '接口分析师',
        description: '分析和设计API接口',
        icon: '🔌'
      },
      {
        id: 'code-reviewer',
        name: '代码审查员',
        description: '代码质量检查和优化建议',
        icon: '👨‍💻'
      }
    ])

    // 方法
    const goBack = () => {
      router.push('/')
    }

    const selectAgent = (agent) => {
      chatStore.selectAgent(agent)
      chatStore.setConnectionStatus(true)
      ElMessage.success(`已选择智能体：${agent.name}`)
    }

    const refreshAgents = () => {
      ElMessage.info('智能体列表已刷新')
    }

    const sendMessage = async () => {
      if (!inputMessage.value.trim() || !currentAgent.value || isLoading.value) {
        return
      }

      const userMessage = {
        type: 'user',
        content: inputMessage.value.trim(),
        agent: null
      }

      // 添加用户消息
      chatStore.addMessage(userMessage)
      
      // 清空输入
      const messageContent = inputMessage.value.trim()
      inputMessage.value = ''
      
      // 设置加载状态
      isLoading.value = true
      
      // 滚动到底部
      await nextTick()
      scrollToBottom()

      try {
        // 模拟智能体响应
        await simulateAgentResponse(messageContent)
      } catch (error) {
        console.error('发送消息失败:', error)
        chatStore.addMessage({
          type: 'error',
          content: '发送消息失败，请重试',
          agent: null
        })
      } finally {
        isLoading.value = false
        await nextTick()
        scrollToBottom()
      }
    }

    const simulateAgentResponse = async (userMessage) => {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const responses = [
        '我理解您的需求，让我来分析一下...',
        '根据您提供的信息，我建议...',
        '这是一个很好的问题，我来为您详细解答...',
        '让我为您生成相应的解决方案...'
      ]
      
      const randomResponse = responses[Math.floor(Math.random() * responses.length)]
      
      chatStore.addMessage({
        type: 'agent',
        content: `${randomResponse}\n\n针对您的问题："${userMessage}"，我的建议是：\n\n1. 首先需要明确具体需求\n2. 分析技术可行性\n3. 制定实施计划\n4. 进行测试验证\n\n如果您需要更详细的分析，请告诉我具体的场景和要求。`,
        agent: currentAgent.value
      })
    }

    const clearMessages = () => {
      chatStore.clearMessages()
      ElMessage.success('对话已清空')
    }

    const scrollToBottom = () => {
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
      }
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString()
    }

    // 监听消息变化，自动滚动
    watch(() => messages.length, () => {
      nextTick(() => {
        scrollToBottom()
      })
    })

    // 组件挂载时的初始化
    onMounted(() => {
      chatStore.setAgents(agents.value)
      // 模拟连接状态
      setTimeout(() => {
        chatStore.setConnectionStatus(true)
      }, 1000)
      
      // 处理从选择页面传递过来的初始消息
      const initialMessage = chatStore.getAndClearInitialMessage()
      if (initialMessage && currentAgent.value) {
        // 自动发送初始消息
        setTimeout(() => {
          inputMessage.value = initialMessage
          sendMessage()
        }, 1500) // 等待连接建立后发送
      }
    })

    return {
      inputMessage,
      messagesContainer,
      isLoading,
      messages,
      currentAgent,
      isConnected,
      agents,
      goBack,
      selectAgent,
      refreshAgents,
      sendMessage,
      clearMessages,
      formatTime
    }
  }
}
</script>

<style scoped>
.agent-chat {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.chat-header {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h2 {
  margin: 0;
  color: #2c3e50;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.message-badge {
  font-size: 18px;
}

.chat-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.chat-sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-header h3 {
  margin: 0;
  color: #2c3e50;
}

.agents-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.agent-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.agent-item:hover {
  background: #f0f9ff;
  border-color: #e1f5fe;
}

.agent-item.active {
  background: #e3f2fd;
  border-color: #409eff;
}

.agent-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 12px;
}

.agent-info {
  flex: 1;
}

.agent-name {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 4px;
}

.agent-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f5f7fa;
}

.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 16px;
}

.empty-messages i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.message-item {
  margin-bottom: 20px;
}

.message-bubble {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
}

.user-message {
  background: #409eff;
  color: white;
  margin-left: auto;
  margin-right: 0;
}

.agent-message {
  background: white;
  border: 1px solid #e4e7ed;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-left: 0;
  margin-right: auto;
}

.agent-message .agent-avatar {
  width: 32px;
  height: 32px;
  font-size: 16px;
  flex-shrink: 0;
}

.message-body {
  flex: 1;
}

.agent-name {
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
  font-size: 14px;
}

.message-content {
  line-height: 1.6;
  white-space: pre-wrap;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.system-message, .error-message {
  background: #f4f4f5;
  color: #909399;
  text-align: center;
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 50%;
}

.error-message {
  background: #fef0f0;
  color: #f56c6c;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 0;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #409eff;
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

.input-area {
  background: white;
  border-top: 1px solid #e4e7ed;
  padding: 20px;
}

.input-container {
  max-width: 100%;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
  margin-top: 12px;
}

.input-actions .el-button i {
  margin-right: 6px;
}

.input-hint {
  text-align: center;
  margin-top: 8px;
}

.input-hint span {
  font-size: 12px;
  color: #909399;
}

.chat-header .el-button i {
  margin-right: 6px;
}

.sidebar-header .el-button i {
  font-size: 16px;
}

.system-message i,
.error-message i {
  margin-right: 8px;
  font-size: 16px;
}

@media (max-width: 768px) {
  .chat-sidebar {
    width: 100%;
    position: fixed;
    z-index: 1000;
    height: 100vh;
    transform: translateX(-100%);
    transition: transform 0.3s;
  }
  
  .chat-sidebar.show {
    transform: translateX(0);
  }
  
  .message-bubble {
    max-width: 85%;
  }
}
</style> 