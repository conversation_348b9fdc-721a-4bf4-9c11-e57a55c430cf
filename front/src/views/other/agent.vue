<template>
    <AppLayout>
    <div class="agent-page">
        <!-- 头部导航 -->
        <!-- <header class="header">
        <div class="container">
            <nav class="nav">
            <div class="logo">
                <img src="/src/assets/images/system.svg" alt="Logo">
                <span>AI智能体平台</span>
            </div>
            <div class="nav-links">
                <router-link to="/" class="nav-link active">首页</router-link>
                <a href="#" class="nav-link">我的智能体</a>
                <a href="#" class="nav-link">AI工具中心</a>
                <a href="#" class="nav-link">使用记录</a>
                <a href="#" class="nav-link">帮助中心</a>
            </div>
            <div class="user-menu">
                <button class="btn btn-icon" @click="toggleTheme">
                <i :class="themeIcon"></i>
                </button>
                <img src="/src/assets/images/agent3.svg" alt="用户头像" class="user-avatar">
            </div>
            </nav>
        </div>
        </header> -->

        <!-- 主内容区 -->
        <main class="main">
        <div class="container">
            <h1>AI智能体平台</h1>
            <p class="subtitle">智能助手和提示词模板，提高您的AI交互体验</p>
            
            <!-- 主要Tab导航 -->
            <div class="main-tabs">
            <div 
                class="main-tab" 
                :class="{ active: activeMainTab === 'agent-library' }"
                @click="setActiveMainTab('agent-library')"
            >
                <i class="ri-robot-line"></i>智能体库
            </div>
            <div 
                class="main-tab" 
                :class="{ active: activeMainTab === 'prompt-library' }"
                @click="setActiveMainTab('prompt-library')"
            >
                <i class="ri-chat-1-line"></i>提示词库
            </div>
            </div>
            
            <!-- 智能体Tab内容 -->
            <div 
            class="main-tab-content" 
            :class="{ active: activeMainTab === 'agent-library' }"
            >
            <!-- 智能体操作工具栏 -->
            <div class="action-toolbar">
                <a href="#" class="btn btn-outline">
                <i class="ri-star-line"></i> 收藏智能体
                </a>
                <a href="#" class="btn btn-outline">
                <i class="ri-history-line"></i> 使用历史
                </a>
            </div>
            
            <!-- 搜索和筛选 -->
            <div class="search-bar">
                <form class="search-form" @submit.prevent="searchAgents">
                <input 
                    type="text" 
                    class="search-input" 
                    placeholder="搜索智能体..."
                    v-model="agentSearchQuery"
                >
                <button type="submit" class="btn btn-primary">搜索</button>
                </form>
                <div class="filter-group">
                <span 
                    v-for="filter in agentFilters" 
                    :key="filter"
                    class="filter-tag" 
                    :class="{ active: activeAgentFilter === filter }"
                    @click="setActiveAgentFilter(filter)"
                >
                    {{ filter }}
                </span>
                </div>
            </div>
            
            <!-- 智能体列表 -->
            <div class="agent-grid">
                <div 
                v-for="agent in agents" 
                :key="agent.id"
                class="card agent-card"
                >
                <div class="agent-card-header">
                    <img :src="agent.icon" alt="智能体图标" class="agent-icon">
                    <div class="agent-name">{{ agent.name }}</div>
                    <div class="agent-status" :class="{ offline: !agent.online }"></div>
                </div>
                <div class="agent-card-body">
                    <p class="agent-description">{{ agent.description }}</p>
                    <div class="agent-stats">
                    <i class="ri-user-line"></i>
                    <span>{{ agent.usageCount }} 次使用</span>
                    </div>
                </div>
                <div class="agent-card-footer">
                    <a href="#" class="btn btn-outline">
                    <i class="ri-information-line" style="margin-right: 0.4rem;"></i>详情
                    </a>
                    <a href="#" class="btn btn-primary">
                    <i class="ri-chat-1-line" style="margin-right: 0.4rem;"></i>开始聊天
                    </a>
                </div>
                </div>
            </div>
            </div>
            
            <!-- 提示词Tab内容 -->
            <div 
            class="main-tab-content" 
            :class="{ active: activeMainTab === 'prompt-library' }"
            >
            <!-- 提示词操作工具栏 -->
            <div class="action-toolbar">
                <button class="btn btn-primary" @click="showPromptModal = true">
                <i class="ri-add-line" style="margin-right: 0.4rem;"></i>创建提示词
                </button>
            </div>
            
            <!-- 搜索和筛选 -->
            <div class="search-bar">
                <form class="search-form" @submit.prevent="searchPrompts">
                <input 
                    type="text" 
                    class="search-input" 
                    placeholder="搜索提示词..."
                    v-model="promptSearchQuery"
                >
                <button type="submit" class="btn btn-primary">搜索</button>
                </form>
                <div class="filter-group">
                <span 
                    v-for="filter in promptFilters" 
                    :key="filter"
                    class="filter-tag" 
                    :class="{ active: activePromptFilter === filter }"
                    @click="setActivePromptFilter(filter)"
                >
                    {{ filter }}
                </span>
                </div>
            </div>
            
            <!-- 分类标签 -->
            <div class="tabs">
                <span 
                v-for="category in promptCategories" 
                :key="category"
                class="tab" 
                :class="{ active: activePromptCategory === category }"
                @click="setActivePromptCategory(category)"
                >
                {{ category }}
                </span>
            </div>
            
            <!-- 提示词卡片列表 -->
            <div class="prompt-grid">
                <div 
                v-for="prompt in prompts" 
                :key="prompt.id"
                class="card prompt-card"
                >
                <div class="prompt-card-header">
                    <div class="prompt-title">{{ prompt.title }}</div>
                    <div class="prompt-category">{{ prompt.category }}</div>
                </div>
                <div class="prompt-card-body">
                    <p class="prompt-description">{{ prompt.description }}</p>
                    <div class="prompt-content">{{ prompt.content }}</div>
                </div>
                <div class="prompt-card-footer">
                    <div class="prompt-stats">
                    <span><i class="ri-star-line"></i> {{ prompt.rating }}</span>
                    <span><i class="ri-time-line"></i> {{ prompt.updatedAt }}</span>
                    </div>
                    <div class="prompt-actions">
                    <button 
                        class="btn btn-outline btn-action" 
                        :title="prompt.isFavorited ? '取消收藏' : '收藏'"
                        @click="toggleFavorite(prompt)"
                    >
                        <i :class="prompt.isFavorited ? 'ri-star-fill' : 'ri-star-line'"></i>
                    </button>
                    <button class="btn btn-outline btn-action" title="编辑">
                        <i class="ri-edit-line"></i>
                    </button>
                    <button class="btn btn-primary btn-action" title="使用">
                        <i class="ri-send-plane-line"></i> 使用
                    </button>
                    </div>
                </div>
                </div>
            </div>
            </div>
        </div>
        </main>

        <!-- 新建提示词模态框 -->
        <div class="modal" v-show="showPromptModal" @click="handleModalClick">
        <div class="modal-content" @click.stop>
            <div class="modal-header">
            <h3 class="modal-title">创建新提示词</h3>
            <span class="modal-close" @click="showPromptModal = false">&times;</span>
            </div>
            <form class="modal-form" @submit.prevent="savePrompt">
            <div class="form-group">
                <label class="form-label">提示词名称</label>
                <input 
                type="text" 
                class="form-input" 
                placeholder="给你的提示词起个名字" 
                v-model="newPrompt.title"
                required
                >
            </div>
            <div class="form-group">
                <label class="form-label">分类</label>
                <select class="form-select" v-model="newPrompt.category" required>
                <option value="">请选择分类</option>
                <option value="writing">写作创作</option>
                <option value="programming">编程开发</option>
                <option value="data">数据分析</option>
                <option value="education">教育学习</option>
                <option value="life">生活助手</option>
                <option value="work">工作效率</option>
                <option value="other">其他</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">描述</label>
                <input 
                type="text" 
                class="form-input" 
                placeholder="简短描述此提示词的用途"
                v-model="newPrompt.description"
                >
            </div>
            <div class="form-group">
                <label class="form-label">提示词内容</label>
                <textarea 
                class="form-textarea" 
                placeholder="输入你的提示词模板..." 
                v-model="newPrompt.content"
                required
                ></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">提示词变量</label>
                <div 
                v-for="(variable, index) in newPrompt.variables" 
                :key="index"
                style="display: flex; gap: 0.5rem; margin-bottom: 0.5rem;"
                >
                <input 
                    type="text" 
                    class="form-input" 
                    placeholder="变量名称" 
                    style="flex: 1;"
                    v-model="variable.name"
                >
                <input 
                    type="text" 
                    class="form-input" 
                    placeholder="默认值" 
                    style="flex: 1;"
                    v-model="variable.defaultValue"
                >
                <button 
                    type="button" 
                    class="btn btn-icon" 
                    style="flex-shrink: 0;"
                    @click="removeVariable(index)"
                >
                    <i class="ri-delete-bin-line"></i>
                </button>
                </div>
                <button type="button" class="btn btn-outline" style="width: 100%;" @click="addVariable">
                <i class="ri-add-line" style="margin-right: 0.4rem;"></i>添加变量
                </button>
            </div>
            <div class="form-group">
                <label class="form-label">可见性</label>
                <div style="display: flex; gap: 1rem;">
                <label style="display: flex; align-items: center; gap: 0.4rem; cursor: pointer;">
                    <input 
                    type="radio" 
                    name="visibility" 
                    value="private" 
                    v-model="newPrompt.visibility"
                    >
                    <span>仅自己可见</span>
                </label>
                <label style="display: flex; align-items: center; gap: 0.4rem; cursor: pointer;">
                    <input 
                    type="radio" 
                    name="visibility" 
                    value="public"
                    v-model="newPrompt.visibility"
                    >
                    <span>公开分享</span>
                </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-cancel" @click="showPromptModal = false">取消</button>
                <button type="submit" class="btn btn-primary">保存提示词</button>
            </div>
            </form>
        </div>
        </div>
    </div>
</AppLayout>
</template>

<script>
export default {
  name: 'AgentPage',
  data() {
    return {
      // 主题相关
      isDarkMode: false,
      
      // 主Tab状态
      activeMainTab: 'agent-library',
      
      // 智能体相关数据
      agentSearchQuery: '',
      activeAgentFilter: '全部',
      agentFilters: ['全部', '工作效率', '教育学习', '创意写作', '数据分析', '编程开发', '生活助手'],
      agents: [
        {
          id: 1,
          name: '智能助手Pro',
          description: '通用型AI助手，能够回答各类问题，提供信息查询，协助完成日常任务。',
          icon: '/src/assets/images/agent1.svg',
          usageCount: '5,238',
          online: true
        },
        {
          id: 2,
          name: '编程助手',
          description: '专注于各种编程语言问题解答和代码优化的智能助手，支持多种编程语言。',
          icon: '/src/assets/images/agent2.svg',
          usageCount: '3,756',
          online: true
        },
        {
          id: 3,
          name: '创意写手',
          description: '帮助用户创作各类文学内容，包括故事、文案、诗歌等创意写作任务。',
          icon: '/src/assets/images/agent3.svg',
          usageCount: '2,543',
          online: true
        },
        {
          id: 4,
          name: '数据分析师',
          description: '专业的数据处理和分析助手，提供数据可视化建议和统计分析支持。',
          icon: '/src/assets/images/agent4.svg',
          usageCount: '1,927',
          online: true
        },
        {
          id: 5,
          name: '学习教练',
          description: '个性化学习助手，提供学习计划、知识讲解和考试准备指导。',
          icon: '/src/assets/images/agent2.svg',
          usageCount: '2,105',
          online: false
        },
        {
          id: 6,
          name: '设计顾问',
          description: '提供UI/UX设计建议、配色方案和设计趋势分析的专业设计助手。',
          icon: '/src/assets/images/agent4.svg',
          usageCount: '1,548',
          online: true
        }
      ],
      
      // 提示词相关数据
      promptSearchQuery: '',
      activePromptFilter: '全部提示词',
      promptFilters: ['全部提示词', '我的提示词', '收藏的提示词', '最近使用'],
      activePromptCategory: '全部',
      promptCategories: ['全部', '写作创作', '编程开发', '数据分析', '教育学习', '生活助手', '工作效率'],
      prompts: [
        {
          id: 1,
          title: '专业写作助手',
          category: '写作创作',
          description: '帮助优化文章结构和语言表达，提供专业写作建议。',
          content: '你是一位专业写作顾问，擅长改进文章结构、增强表达力和修正语法错误。请帮我优化以下文章，使其更加专业、流畅且引人入胜。',
          rating: '4.8',
          updatedAt: '2天前更新',
          isFavorited: false
        },
        {
          id: 2,
          title: '代码优化专家',
          category: '编程开发',
          description: '检查并优化代码，提供性能改进建议和最佳实践。',
          content: '你是一位经验丰富的软件工程师，熟悉各种编程语言的最佳实践和性能优化技巧。请审查我的代码，提供以下改进建议：\n1. 代码效率和性能优化\n2. 代码可读性和结构改进\n3. 潜在的bug和安全问题\n4. 遵循行业标准和最佳实践',
          rating: '4.9',
          updatedAt: '1周前更新',
          isFavorited: false
        },
        {
          id: 3,
          title: '数据分析指导',
          category: '数据分析',
          description: '指导数据处理流程，提供分析方法建议和可视化策略。',
          content: '作为一名数据分析专家，请帮我分析以下数据集并提供深入见解。请包括：\n1. 数据集的基本统计分析\n2. 主要趋势和模式识别\n3. 可能的相关性和因果关系\n4. 基于数据的actionable建议\n5. 适合这些数据的可视化方式推荐',
          rating: '4.7',
          updatedAt: '3天前更新',
          isFavorited: false
        },
        {
          id: 4,
          title: '学习计划制定',
          category: '教育学习',
          description: '根据学习目标和时间安排，制定个性化学习计划。',
          content: '请作为一名专业的学习规划顾问，根据我提供的以下信息，制定一个个性化的学习计划：\n1. 学习目标\n2. 可用时间\n3. 当前知识水平\n4. 学习风格偏好\n5. 学习资源限制\n\n请提供详细的每周计划，包括学习内容分解、时间分配、推荐资源和进度跟踪方法。',
          rating: '4.6',
          updatedAt: '5天前更新',
          isFavorited: false
        }
      ],
      
      // 模态框相关
      showPromptModal: false,
      newPrompt: {
        title: '',
        category: '',
        description: '',
        content: '',
        variables: [],
        visibility: 'private'
      }
    }
  },
  computed: {
    themeIcon() {
      return this.isDarkMode ? 'ri-sun-line' : 'ri-moon-line'
    }
  },
  methods: {
    // 主题切换
    toggleTheme() {
      this.isDarkMode = !this.isDarkMode
      document.body.classList.toggle('dark-mode', this.isDarkMode)
    },
    
    // 主Tab切换
    setActiveMainTab(tab) {
      this.activeMainTab = tab
    },
    
    // 智能体筛选
    setActiveAgentFilter(filter) {
      this.activeAgentFilter = filter
    },
    
    // 搜索智能体
    searchAgents() {
      console.log('搜索智能体:', this.agentSearchQuery)
      // 这里可以添加实际的搜索逻辑
    },
    
    // 提示词筛选
    setActivePromptFilter(filter) {
      this.activePromptFilter = filter
    },
    
    // 提示词分类
    setActivePromptCategory(category) {
      this.activePromptCategory = category
    },
    
    // 搜索提示词
    searchPrompts() {
      console.log('搜索提示词:', this.promptSearchQuery)
      // 这里可以添加实际的搜索逻辑
    },
    
    // 收藏切换
    toggleFavorite(prompt) {
      prompt.isFavorited = !prompt.isFavorited
    },
    
    // 模态框点击处理
    handleModalClick(event) {
      if (event.target === event.currentTarget) {
        this.showPromptModal = false
      }
    },
    
    // 添加变量
    addVariable() {
      this.newPrompt.variables.push({
        name: '',
        defaultValue: ''
      })
    },
    
    // 删除变量
    removeVariable(index) {
      this.newPrompt.variables.splice(index, 1)
    },
    
    // 保存提示词
    savePrompt() {
      console.log('保存提示词:', this.newPrompt)
      // 这里可以添加实际的保存逻辑
      
      // 重置表单
      this.newPrompt = {
        title: '',
        category: '',
        description: '',
        content: '',
        variables: [],
        visibility: 'private'
      }
      this.showPromptModal = false
    }
  }
}
</script>

<style scoped>
/* Tab导航样式 */
.main-tabs {
  display: flex;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.main-tab {
  padding: 1rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-light);
  background-color: transparent;
  position: relative;
  border-bottom: 2px solid transparent;
}

.main-tab:hover {
  color: var(--primary-color);
}

.main-tab.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
}

.main-tab i {
  margin-right: 0.5rem;
  font-size: 1.2rem;
  vertical-align: middle;
}

.main-tab-content {
  display: none;
}

.main-tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 辅助工具栏样式 */
.action-toolbar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
}

.action-toolbar .btn {
  margin-left: 0.5rem;
}

/* 其他样式继承全局CSS */
</style> 