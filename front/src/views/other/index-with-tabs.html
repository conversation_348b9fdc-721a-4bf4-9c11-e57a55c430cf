<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI智能体管理平台 - 首页</title>
  <link rel="stylesheet" href="static/css/style.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css">
  <style>
    /* Tab导航样式 */
    .main-tabs {
      display: flex;
      margin-bottom: 1.5rem;
      border-bottom: 1px solid var(--border-color);
    }
    
    .main-tab {
      padding: 1rem 1.5rem;
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition);
      color: var(--text-light);
      background-color: transparent;
      position: relative;
      border-bottom: 2px solid transparent;
    }
    
    .main-tab:hover {
      color: var(--primary-color);
    }
    
    .main-tab.active {
      color: var(--primary-color);
      border-bottom: 2px solid var(--primary-color);
    }
    
    .main-tab i {
      margin-right: 0.5rem;
      font-size: 1.2rem;
      vertical-align: middle;
    }
    
    .main-tab-content {
      display: none;
    }
    
    .main-tab-content.active {
      display: block;
      animation: fadeIn 0.3s ease;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    /* 辅助工具栏样式 */
    .action-toolbar {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 1rem;
    }
    
    .action-toolbar .btn {
      margin-left: 0.5rem;
    }
  </style>
</head>
<body>
  <!-- 头部导航 -->
  <header class="header">
    <div class="container">
      <nav class="nav">
        <div class="logo">
          <img src="static/images/logo-placeholder.svg" alt="Logo">
          <span>AI智能体平台</span>
        </div>
        <div class="nav-links">
          <a href="index-with-tabs.html" class="nav-link active">首页</a>
          <a href="#" class="nav-link">我的智能体</a>
          <a href="tools-center.html" class="nav-link">AI工具中心</a>
          <a href="#" class="nav-link">使用记录</a>
          <a href="#" class="nav-link">帮助中心</a>
        </div>
        <div class="user-menu">
          <button class="btn btn-icon" id="theme-toggle">
            <i class="ri-moon-line"></i>
          </button>
          <img src="static/images/agent3.svg" alt="用户头像" class="user-avatar">
        </div>
      </nav>
    </div>
  </header>

  <!-- 主内容区 -->
  <main class="main">
    <div class="container">
      <h1>AI智能体平台</h1>
      <p class="subtitle">智能助手和提示词模板，提高您的AI交互体验</p>
      
      <!-- 主要Tab导航 -->
      <div class="main-tabs">
        <div class="main-tab active" data-tab="agent-library">
          <i class="ri-robot-line"></i>智能体库
        </div>
        <div class="main-tab" data-tab="prompt-library">
          <i class="ri-chat-1-line"></i>提示词库
        </div>
      </div>
      
      <!-- 智能体Tab内容 -->
      <div class="main-tab-content active" id="agent-library">
        <!-- 智能体操作工具栏 -->
        <div class="action-toolbar">
          <a href="#" class="btn btn-outline">
            <i class="ri-star-line"></i> 收藏智能体
          </a>
          <a href="#" class="btn btn-outline">
            <i class="ri-history-line"></i> 使用历史
          </a>
        </div>
        
        <!-- 搜索和筛选 -->
        <div class="search-bar">
          <form class="search-form">
            <input type="text" class="search-input" placeholder="搜索智能体...">
            <button type="submit" class="btn btn-primary">搜索</button>
          </form>
          <div class="filter-group">
            <span class="filter-tag active">全部</span>
            <span class="filter-tag">工作效率</span>
            <span class="filter-tag">教育学习</span>
            <span class="filter-tag">创意写作</span>
            <span class="filter-tag">数据分析</span>
            <span class="filter-tag">编程开发</span>
            <span class="filter-tag">生活助手</span>
          </div>
        </div>
        
        <!-- 智能体列表 -->
        <div class="agent-grid">
          <!-- 智能体卡片 1 -->
          <div class="card agent-card">
            <div class="agent-card-header">
              <img src="static/images/agent1.svg" alt="智能体图标" class="agent-icon">
              <div class="agent-name">智能助手Pro</div>
              <div class="agent-status"></div>
            </div>
            <div class="agent-card-body">
              <p class="agent-description">通用型AI助手，能够回答各类问题，提供信息查询，协助完成日常任务。</p>
              <div class="agent-stats">
                <i class="ri-user-line"></i>
                <span>5,238 次使用</span>
              </div>
            </div>
            <div class="agent-card-footer">
              <a href="agent-detail.html" class="btn btn-outline">
                <i class="ri-information-line" style="margin-right: 0.4rem;"></i>详情
              </a>
              <a href="chat.html" class="btn btn-primary">
                <i class="ri-chat-1-line" style="margin-right: 0.4rem;"></i>开始聊天
              </a>
            </div>
          </div>
          
          <!-- 智能体卡片 2 -->
          <div class="card agent-card">
            <div class="agent-card-header">
              <img src="static/images/agent2.svg" alt="智能体图标" class="agent-icon">
              <div class="agent-name">编程助手</div>
              <div class="agent-status"></div>
            </div>
            <div class="agent-card-body">
              <p class="agent-description">专注于各种编程语言问题解答和代码优化的智能助手，支持多种编程语言。</p>
              <div class="agent-stats">
                <i class="ri-user-line"></i>
                <span>3,756 次使用</span>
              </div>
            </div>
            <div class="agent-card-footer">
              <a href="agent-detail.html" class="btn btn-outline">
                <i class="ri-information-line" style="margin-right: 0.4rem;"></i>详情
              </a>
              <a href="chat.html" class="btn btn-primary">
                <i class="ri-chat-1-line" style="margin-right: 0.4rem;"></i>开始聊天
              </a>
            </div>
          </div>
          
          <!-- 智能体卡片 3 -->
          <div class="card agent-card">
            <div class="agent-card-header">
              <img src="static/images/agent3.svg" alt="智能体图标" class="agent-icon">
              <div class="agent-name">创意写手</div>
              <div class="agent-status"></div>
            </div>
            <div class="agent-card-body">
              <p class="agent-description">帮助用户创作各类文学内容，包括故事、文案、诗歌等创意写作任务。</p>
              <div class="agent-stats">
                <i class="ri-user-line"></i>
                <span>2,543 次使用</span>
              </div>
            </div>
            <div class="agent-card-footer">
              <a href="agent-detail.html" class="btn btn-outline">
                <i class="ri-information-line" style="margin-right: 0.4rem;"></i>详情
              </a>
              <a href="chat.html" class="btn btn-primary">
                <i class="ri-chat-1-line" style="margin-right: 0.4rem;"></i>开始聊天
              </a>
            </div>
          </div>
          
          <!-- 智能体卡片 4 -->
          <div class="card agent-card">
            <div class="agent-card-header">
              <img src="static/images/agent4.svg" alt="智能体图标" class="agent-icon">
              <div class="agent-name">数据分析师</div>
              <div class="agent-status"></div>
            </div>
            <div class="agent-card-body">
              <p class="agent-description">专业的数据处理和分析助手，提供数据可视化建议和统计分析支持。</p>
              <div class="agent-stats">
                <i class="ri-user-line"></i>
                <span>1,927 次使用</span>
              </div>
            </div>
            <div class="agent-card-footer">
              <a href="agent-detail.html" class="btn btn-outline">
                <i class="ri-information-line" style="margin-right: 0.4rem;"></i>详情
              </a>
              <a href="chat.html" class="btn btn-primary">
                <i class="ri-chat-1-line" style="margin-right: 0.4rem;"></i>开始聊天
              </a>
            </div>
          </div>
          
          <!-- 智能体卡片 5 -->
          <div class="card agent-card">
            <div class="agent-card-header">
              <img src="static/images/agent2.svg" alt="智能体图标" class="agent-icon">
              <div class="agent-name">学习教练</div>
              <div class="agent-status offline"></div>
            </div>
            <div class="agent-card-body">
              <p class="agent-description">个性化学习助手，提供学习计划、知识讲解和考试准备指导。</p>
              <div class="agent-stats">
                <i class="ri-user-line"></i>
                <span>2,105 次使用</span>
              </div>
            </div>
            <div class="agent-card-footer">
              <a href="agent-detail.html" class="btn btn-outline">
                <i class="ri-information-line" style="margin-right: 0.4rem;"></i>详情
              </a>
              <a href="chat.html" class="btn btn-primary">
                <i class="ri-chat-1-line" style="margin-right: 0.4rem;"></i>开始聊天
              </a>
            </div>
          </div>
          
          <!-- 智能体卡片 6 -->
          <div class="card agent-card">
            <div class="agent-card-header">
              <img src="static/images/agent4.svg" alt="智能体图标" class="agent-icon">
              <div class="agent-name">设计顾问</div>
              <div class="agent-status"></div>
            </div>
            <div class="agent-card-body">
              <p class="agent-description">提供UI/UX设计建议、配色方案和设计趋势分析的专业设计助手。</p>
              <div class="agent-stats">
                <i class="ri-user-line"></i>
                <span>1,548 次使用</span>
              </div>
            </div>
            <div class="agent-card-footer">
              <a href="agent-detail.html" class="btn btn-outline">
                <i class="ri-information-line" style="margin-right: 0.4rem;"></i>详情
              </a>
              <a href="chat.html" class="btn btn-primary">
                <i class="ri-chat-1-line" style="margin-right: 0.4rem;"></i>开始聊天
              </a>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 提示词Tab内容 -->
      <div class="main-tab-content" id="prompt-library">
        <!-- 提示词操作工具栏 -->
        <div class="action-toolbar">
          <button class="btn btn-primary" id="new-prompt-btn">
            <i class="ri-add-line" style="margin-right: 0.4rem;"></i>创建提示词
          </button>
        </div>
        
        <!-- 搜索和筛选 -->
        <div class="search-bar">
          <form class="search-form">
            <input type="text" class="search-input" placeholder="搜索提示词...">
            <button type="submit" class="btn btn-primary">搜索</button>
          </form>
          <div class="filter-group">
            <span class="filter-tag active">全部提示词</span>
            <span class="filter-tag">我的提示词</span>
            <span class="filter-tag">收藏的提示词</span>
            <span class="filter-tag">最近使用</span>
          </div>
        </div>
        
        <!-- 分类标签 -->
        <div class="tabs">
          <span class="tab active">全部</span>
          <span class="tab">写作创作</span>
          <span class="tab">编程开发</span>
          <span class="tab">数据分析</span>
          <span class="tab">教育学习</span>
          <span class="tab">生活助手</span>
          <span class="tab">工作效率</span>
        </div>
        
        <!-- 提示词卡片列表 -->
        <div class="prompt-grid">
          <!-- 提示词卡片 1 -->
          <div class="card prompt-card">
            <div class="prompt-card-header">
              <div class="prompt-title">专业写作助手</div>
              <div class="prompt-category">写作创作</div>
            </div>
            <div class="prompt-card-body">
              <p class="prompt-description">帮助优化文章结构和语言表达，提供专业写作建议。</p>
              <div class="prompt-content">你是一位专业写作顾问，擅长改进文章结构、增强表达力和修正语法错误。请帮我优化以下文章，使其更加专业、流畅且引人入胜。</div>
            </div>
            <div class="prompt-card-footer">
              <div class="prompt-stats">
                <span><i class="ri-star-line"></i> 4.8</span>
                <span><i class="ri-time-line"></i> 2天前更新</span>
              </div>
              <div class="prompt-actions">
                <button class="btn btn-outline btn-action" title="收藏">
                  <i class="ri-star-line"></i>
                </button>
                <button class="btn btn-outline btn-action" title="编辑">
                  <i class="ri-edit-line"></i>
                </button>
                <button class="btn btn-primary btn-action" title="使用">
                  <i class="ri-send-plane-line"></i> 使用
                </button>
              </div>
            </div>
          </div>
          
          <!-- 提示词卡片 2 -->
          <div class="card prompt-card">
            <div class="prompt-card-header">
              <div class="prompt-title">代码优化专家</div>
              <div class="prompt-category">编程开发</div>
            </div>
            <div class="prompt-card-body">
              <p class="prompt-description">检查并优化代码，提供性能改进建议和最佳实践。</p>
              <div class="prompt-content">你是一位经验丰富的软件工程师，熟悉各种编程语言的最佳实践和性能优化技巧。请审查我的代码，提供以下改进建议：
1. 代码效率和性能优化
2. 代码可读性和结构改进
3. 潜在的bug和安全问题
4. 遵循行业标准和最佳实践</div>
            </div>
            <div class="prompt-card-footer">
              <div class="prompt-stats">
                <span><i class="ri-star-line"></i> 4.9</span>
                <span><i class="ri-time-line"></i> 1周前更新</span>
              </div>
              <div class="prompt-actions">
                <button class="btn btn-outline btn-action" title="收藏">
                  <i class="ri-star-line"></i>
                </button>
                <button class="btn btn-outline btn-action" title="编辑">
                  <i class="ri-edit-line"></i>
                </button>
                <button class="btn btn-primary btn-action" title="使用">
                  <i class="ri-send-plane-line"></i> 使用
                </button>
              </div>
            </div>
          </div>
          
          <!-- 提示词卡片 3 -->
          <div class="card prompt-card">
            <div class="prompt-card-header">
              <div class="prompt-title">数据分析指导</div>
              <div class="prompt-category">数据分析</div>
            </div>
            <div class="prompt-card-body">
              <p class="prompt-description">指导数据处理流程，提供分析方法建议和可视化策略。</p>
              <div class="prompt-content">作为一名数据分析专家，请帮我分析以下数据集并提供深入见解。请包括：
1. 数据集的基本统计分析
2. 主要趋势和模式识别
3. 可能的相关性和因果关系
4. 基于数据的actionable建议
5. 适合这些数据的可视化方式推荐</div>
            </div>
            <div class="prompt-card-footer">
              <div class="prompt-stats">
                <span><i class="ri-star-line"></i> 4.7</span>
                <span><i class="ri-time-line"></i> 3天前更新</span>
              </div>
              <div class="prompt-actions">
                <button class="btn btn-outline btn-action" title="收藏">
                  <i class="ri-star-line"></i>
                </button>
                <button class="btn btn-outline btn-action" title="编辑">
                  <i class="ri-edit-line"></i>
                </button>
                <button class="btn btn-primary btn-action" title="使用">
                  <i class="ri-send-plane-line"></i> 使用
                </button>
              </div>
            </div>
          </div>
          
          <!-- 提示词卡片 4 -->
          <div class="card prompt-card">
            <div class="prompt-card-header">
              <div class="prompt-title">学习计划制定</div>
              <div class="prompt-category">教育学习</div>
            </div>
            <div class="prompt-card-body">
              <p class="prompt-description">根据学习目标和时间安排，制定个性化学习计划。</p>
              <div class="prompt-content">请作为一名专业的学习规划顾问，根据我提供的以下信息，制定一个个性化的学习计划：
1. 学习目标
2. 可用时间
3. 当前知识水平
4. 学习风格偏好
5. 学习资源限制

请提供详细的每周计划，包括学习内容分解、时间分配、推荐资源和进度跟踪方法。</div>
            </div>
            <div class="prompt-card-footer">
              <div class="prompt-stats">
                <span><i class="ri-star-line"></i> 4.6</span>
                <span><i class="ri-time-line"></i> 5天前更新</span>
              </div>
              <div class="prompt-actions">
                <button class="btn btn-outline btn-action" title="收藏">
                  <i class="ri-star-line"></i>
                </button>
                <button class="btn btn-outline btn-action" title="编辑">
                  <i class="ri-edit-line"></i>
                </button>
                <button class="btn btn-primary btn-action" title="使用">
                  <i class="ri-send-plane-line"></i> 使用
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- 新建提示词模态框 -->
  <div class="modal" id="prompt-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">创建新提示词</h3>
        <span class="modal-close">&times;</span>
      </div>
      <form class="modal-form">
        <div class="form-group">
          <label class="form-label">提示词名称</label>
          <input type="text" class="form-input" placeholder="给你的提示词起个名字" required>
        </div>
        <div class="form-group">
          <label class="form-label">分类</label>
          <select class="form-select" required>
            <option value="">请选择分类</option>
            <option value="writing">写作创作</option>
            <option value="programming">编程开发</option>
            <option value="data">数据分析</option>
            <option value="education">教育学习</option>
            <option value="life">生活助手</option>
            <option value="work">工作效率</option>
            <option value="other">其他</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label">描述</label>
          <input type="text" class="form-input" placeholder="简短描述此提示词的用途">
        </div>
        <div class="form-group">
          <label class="form-label">提示词内容</label>
          <textarea class="form-textarea" placeholder="输入你的提示词模板..." required></textarea>
        </div>
        <div class="form-group">
          <label class="form-label">提示词变量</label>
          <div style="display: flex; gap: 0.5rem; margin-bottom: 0.5rem;">
            <input type="text" class="form-input" placeholder="变量名称" style="flex: 1;">
            <input type="text" class="form-input" placeholder="默认值" style="flex: 1;">
            <button type="button" class="btn btn-icon" style="flex-shrink: 0;">
              <i class="ri-delete-bin-line"></i>
            </button>
          </div>
          <button type="button" class="btn btn-outline" style="width: 100%;">
            <i class="ri-add-line" style="margin-right: 0.4rem;"></i>添加变量
          </button>
        </div>
        <div class="form-group">
          <label class="form-label">可见性</label>
          <div style="display: flex; gap: 1rem;">
            <label style="display: flex; align-items: center; gap: 0.4rem; cursor: pointer;">
              <input type="radio" name="visibility" value="private" checked>
              <span>仅自己可见</span>
            </label>
            <label style="display: flex; align-items: center; gap: 0.4rem; cursor: pointer;">
              <input type="radio" name="visibility" value="public">
              <span>公开分享</span>
            </label>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-cancel">取消</button>
          <button type="submit" class="btn btn-primary">保存提示词</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    // 主题切换功能
    const themeToggle = document.getElementById('theme-toggle');
    const body = document.body;
    
    themeToggle.addEventListener('click', () => {
      body.classList.toggle('dark-mode');
      const icon = themeToggle.querySelector('i');
      
      if (body.classList.contains('dark-mode')) {
        icon.classList.remove('ri-moon-line');
        icon.classList.add('ri-sun-line');
      } else {
        icon.classList.remove('ri-sun-line');
        icon.classList.add('ri-moon-line');
      }
    });
    
    // 筛选标签切换
    const filterTags = document.querySelectorAll('.filter-tag');
    filterTags.forEach(tag => {
      tag.addEventListener('click', () => {
        filterTags.forEach(t => t.classList.remove('active'));
        tag.classList.add('active');
      });
    });
    
    // 分类标签切换
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        tabs.forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
      });
    });
    
    // 主Tab切换
    const mainTabs = document.querySelectorAll('.main-tab');
    mainTabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // 移除所有tab和内容的active类
        mainTabs.forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.main-tab-content').forEach(c => c.classList.remove('active'));
        
        // 添加当前tab和对应内容的active类
        tab.classList.add('active');
        const tabId = tab.getAttribute('data-tab');
        document.getElementById(tabId).classList.add('active');
      });
    });
    
    // 模态框功能
    const modal = document.getElementById('prompt-modal');
    const newPromptBtn = document.getElementById('new-prompt-btn');
    const closeBtn = document.querySelector('.modal-close');
    const cancelBtn = document.querySelector('.btn-cancel');
    
    newPromptBtn.addEventListener('click', () => {
      modal.style.display = 'block';
    });
    
    closeBtn.addEventListener('click', () => {
      modal.style.display = 'none';
    });
    
    cancelBtn.addEventListener('click', () => {
      modal.style.display = 'none';
    });
    
    window.addEventListener('click', (event) => {
      if (event.target === modal) {
        modal.style.display = 'none';
      }
    });
    
    // 收藏功能
    const starBtns = document.querySelectorAll('.prompt-actions .ri-star-line');
    starBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        if (btn.classList.contains('ri-star-line')) {
          btn.classList.remove('ri-star-line');
          btn.classList.add('ri-star-fill');
          btn.parentElement.setAttribute('title', '取消收藏');
        } else {
          btn.classList.remove('ri-star-fill');
          btn.classList.add('ri-star-line');
          btn.parentElement.setAttribute('title', '收藏');
        }
      });
    });
  </script>
</body>
</html> 