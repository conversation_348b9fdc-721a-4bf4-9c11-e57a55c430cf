<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI智能体管理平台 - 提示词库</title>
  <link rel="stylesheet" href="static/css/style.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css">
  <style>
    /* 提示词库特有样式 */
    .prompt-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 1.25rem;
      margin-top: 1.25rem;
    }
    
    .prompt-card {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    
    .prompt-card-header {
      padding: 1.2rem 1.2rem 0.8rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .prompt-title {
      font-weight: 600;
      font-size: 1.1rem;
      color: var(--text-color);
    }
    
    .prompt-category {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 12px;
      background-color: rgba(var(--primary-color-rgb), 0.1);
      color: var(--primary-color);
    }
    
    .prompt-card-body {
      padding: 0 1.2rem 1.2rem;
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    
    .prompt-description {
      color: var(--text-light);
      font-size: 0.9rem;
      margin-bottom: 1rem;
      line-height: 1.5;
    }
    
    .prompt-content {
      background-color: var(--background-color);
      padding: 0.8rem;
      border-radius: var(--radius-sm);
      font-size: 0.85rem;
      margin-bottom: 1rem;
      color: var(--text-color);
      max-height: 100px;
      overflow-y: auto;
      flex: 1;
      white-space: pre-wrap;
    }
    
    .prompt-card-footer {
      padding: 1.2rem;
      display: flex;
      justify-content: space-between;
      border-top: 1px solid var(--border-color);
      align-items: center;
    }
    
    .prompt-stats {
      display: flex;
      align-items: center;
      gap: 0.8rem;
      font-size: 0.8rem;
      color: var(--text-light);
    }
    
    .prompt-actions {
      display: flex;
      gap: 0.5rem;
    }
    
    .tab-section {
      margin-bottom: 2rem;
    }
    
    /* 新建提示词模态框 */
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      backdrop-filter: blur(4px);
      -webkit-backdrop-filter: blur(4px);
    }
    
    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 90%;
      max-width: 600px;
      background-color: var(--card-bg-color);
      border-radius: var(--radius);
      box-shadow: var(--shadow-lg);
      padding: 1.5rem;
    }
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
    }
    
    .modal-title {
      font-size: 1.2rem;
      font-weight: 600;
    }
    
    .modal-close {
      cursor: pointer;
      font-size: 1.3rem;
      color: var(--text-light);
      transition: var(--transition);
    }
    
    .modal-close:hover {
      color: var(--text-color);
    }
    
    .modal-form {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .form-label {
      font-size: 0.9rem;
      font-weight: 500;
      color: var(--text-color);
    }
    
    .form-input {
      padding: 0.8rem;
      border: none;
      border-radius: var(--radius-sm);
      background-color: var(--background-color);
      color: var(--text-color);
    }
    
    .form-textarea {
      padding: 0.8rem;
      border: none;
      border-radius: var(--radius-sm);
      background-color: var(--background-color);
      color: var(--text-color);
      resize: vertical;
      min-height: 120px;
      font-family: inherit;
    }
    
    .form-select {
      padding: 0.8rem;
      border: none;
      border-radius: var(--radius-sm);
      background-color: var(--background-color);
      color: var(--text-color);
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%235d7290' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 0.8rem center;
      background-size: 16px 12px;
    }
    
    .form-input:focus, .form-textarea:focus, .form-select:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.15);
    }
    
    .modal-footer {
      display: flex;
      justify-content: flex-end;
      gap: 0.8rem;
      margin-top: 1.5rem;
    }
    
    .btn-cancel {
      background-color: var(--background-color);
      color: var(--text-color);
    }
    
    /* 添加一个新的自定义按钮类型 */
    .btn-action {
      padding: 0.4rem 0.6rem;
      font-size: 0.8rem;
      border-radius: var(--radius-sm);
    }
  </style>
</head>
<body>
  <!-- 头部导航 -->
  <header class="header">
    <div class="container">
      <nav class="nav">
        <div class="logo">
          <img src="static/images/logo-placeholder.svg" alt="Logo">
          <span>AI智能体平台</span>
        </div>
        <div class="nav-links">
          <a href="index.html" class="nav-link">首页</a>
          <a href="#" class="nav-link">我的智能体</a>
          <a href="prompt-library.html" class="nav-link active">提示词库</a>
          <a href="#" class="nav-link">使用记录</a>
          <a href="#" class="nav-link">帮助中心</a>
        </div>
        <div class="user-menu">
          <button class="btn btn-icon" id="theme-toggle">
            <i class="ri-moon-line"></i>
          </button>
          <img src="static/images/agent3.svg" alt="用户头像" class="user-avatar">
        </div>
      </nav>
    </div>
  </header>

  <!-- 主内容区 -->
  <main class="main">
    <div class="container">
      <h1>提示词库</h1>
      <p class="subtitle">管理和使用你的AI提示词模板，提高智能体效率</p>
      
      <!-- 顶部操作栏 -->
      <div class="search-bar">
        <form class="search-form">
          <input type="text" class="search-input" placeholder="搜索提示词...">
          <button type="submit" class="btn btn-primary">搜索</button>
        </form>
        <div class="filter-group">
          <span class="filter-tag active">全部提示词</span>
          <span class="filter-tag">我的提示词</span>
          <span class="filter-tag">收藏的提示词</span>
          <span class="filter-tag">最近使用</span>
        </div>
      </div>
      
      <!-- 分类标签和新建按钮 -->
      <div class="tab-section">
        <div class="tabs">
          <span class="tab active">全部</span>
          <span class="tab">写作创作</span>
          <span class="tab">编程开发</span>
          <span class="tab">数据分析</span>
          <span class="tab">教育学习</span>
          <span class="tab">生活助手</span>
          <span class="tab">工作效率</span>
        </div>
        <button class="btn btn-primary" id="new-prompt-btn" style="float: right; margin-top: -40px;">
          <i class="ri-add-line" style="margin-right: 0.4rem;"></i>创建提示词
        </button>
      </div>
      
      <!-- 提示词卡片列表 -->
      <div class="prompt-grid">
        <!-- 提示词卡片 1 -->
        <div class="card prompt-card">
          <div class="prompt-card-header">
            <div class="prompt-title">专业写作助手</div>
            <div class="prompt-category">写作创作</div>
          </div>
          <div class="prompt-card-body">
            <p class="prompt-description">帮助优化文章结构和语言表达，提供专业写作建议。</p>
            <div class="prompt-content">你是一位专业写作顾问，擅长改进文章结构、增强表达力和修正语法错误。请帮我优化以下文章，使其更加专业、流畅且引人入胜。</div>
          </div>
          <div class="prompt-card-footer">
            <div class="prompt-stats">
              <span><i class="ri-star-line"></i> 4.8</span>
              <span><i class="ri-time-line"></i> 2天前更新</span>
            </div>
            <div class="prompt-actions">
              <button class="btn btn-outline btn-action" title="收藏">
                <i class="ri-star-line"></i>
              </button>
              <button class="btn btn-outline btn-action" title="编辑">
                <i class="ri-edit-line"></i>
              </button>
              <button class="btn btn-primary btn-action" title="使用">
                <i class="ri-send-plane-line"></i> 使用
              </button>
            </div>
          </div>
        </div>
        
        <!-- 提示词卡片 2 -->
        <div class="card prompt-card">
          <div class="prompt-card-header">
            <div class="prompt-title">代码优化专家</div>
            <div class="prompt-category">编程开发</div>
          </div>
          <div class="prompt-card-body">
            <p class="prompt-description">检查并优化代码，提供性能改进建议和最佳实践。</p>
            <div class="prompt-content">你是一位经验丰富的软件工程师，熟悉各种编程语言的最佳实践和性能优化技巧。请审查我的代码，提供以下改进建议：
1. 代码效率和性能优化
2. 代码可读性和结构改进
3. 潜在的bug和安全问题
4. 遵循行业标准和最佳实践</div>
          </div>
          <div class="prompt-card-footer">
            <div class="prompt-stats">
              <span><i class="ri-star-line"></i> 4.9</span>
              <span><i class="ri-time-line"></i> 1周前更新</span>
            </div>
            <div class="prompt-actions">
              <button class="btn btn-outline btn-action" title="收藏">
                <i class="ri-star-line"></i>
              </button>
              <button class="btn btn-outline btn-action" title="编辑">
                <i class="ri-edit-line"></i>
              </button>
              <button class="btn btn-primary btn-action" title="使用">
                <i class="ri-send-plane-line"></i> 使用
              </button>
            </div>
          </div>
        </div>
        
        <!-- 提示词卡片 3 -->
        <div class="card prompt-card">
          <div class="prompt-card-header">
            <div class="prompt-title">数据分析指导</div>
            <div class="prompt-category">数据分析</div>
          </div>
          <div class="prompt-card-body">
            <p class="prompt-description">指导数据处理流程，提供分析方法建议和可视化策略。</p>
            <div class="prompt-content">作为一名数据分析专家，请帮我分析以下数据集并提供深入见解。请包括：
1. 数据集的基本统计分析
2. 主要趋势和模式识别
3. 可能的相关性和因果关系
4. 基于数据的actionable建议
5. 适合这些数据的可视化方式推荐</div>
          </div>
          <div class="prompt-card-footer">
            <div class="prompt-stats">
              <span><i class="ri-star-line"></i> 4.7</span>
              <span><i class="ri-time-line"></i> 3天前更新</span>
            </div>
            <div class="prompt-actions">
              <button class="btn btn-outline btn-action" title="收藏">
                <i class="ri-star-line"></i>
              </button>
              <button class="btn btn-outline btn-action" title="编辑">
                <i class="ri-edit-line"></i>
              </button>
              <button class="btn btn-primary btn-action" title="使用">
                <i class="ri-send-plane-line"></i> 使用
              </button>
            </div>
          </div>
        </div>
        
        <!-- 提示词卡片 4 -->
        <div class="card prompt-card">
          <div class="prompt-card-header">
            <div class="prompt-title">学习计划制定</div>
            <div class="prompt-category">教育学习</div>
          </div>
          <div class="prompt-card-body">
            <p class="prompt-description">根据学习目标和时间安排，制定个性化学习计划。</p>
            <div class="prompt-content">请作为一名专业的学习规划顾问，根据我提供的以下信息，制定一个个性化的学习计划：
1. 学习目标
2. 可用时间
3. 当前知识水平
4. 学习风格偏好
5. 学习资源限制

请提供详细的每周计划，包括学习内容分解、时间分配、推荐资源和进度跟踪方法。</div>
          </div>
          <div class="prompt-card-footer">
            <div class="prompt-stats">
              <span><i class="ri-star-line"></i> 4.6</span>
              <span><i class="ri-time-line"></i> 5天前更新</span>
            </div>
            <div class="prompt-actions">
              <button class="btn btn-outline btn-action" title="收藏">
                <i class="ri-star-line"></i>
              </button>
              <button class="btn btn-outline btn-action" title="编辑">
                <i class="ri-edit-line"></i>
              </button>
              <button class="btn btn-primary btn-action" title="使用">
                <i class="ri-send-plane-line"></i> 使用
              </button>
            </div>
          </div>
        </div>
        
        <!-- 提示词卡片 5 -->
        <div class="card prompt-card">
          <div class="prompt-card-header">
            <div class="prompt-title">健康饮食顾问</div>
            <div class="prompt-category">生活助手</div>
          </div>
          <div class="prompt-card-body">
            <p class="prompt-description">提供健康饮食建议，制定个性化膳食计划和营养指导。</p>
            <div class="prompt-content">作为一名营养学专家，请根据我提供的个人信息（年龄、性别、身高、体重、活动水平、健康目标和饮食偏好），制定一份为期7天的健康饮食计划。计划应包括：
1. 每日三餐和零食的详细食谱
2. 食材清单和准备方法
3. 营养成分分析
4. 如何根据个人喜好进行调整的建议
5. 饮食相关的健康提示</div>
          </div>
          <div class="prompt-card-footer">
            <div class="prompt-stats">
              <span><i class="ri-star-line"></i> 4.5</span>
              <span><i class="ri-time-line"></i> 1周前更新</span>
            </div>
            <div class="prompt-actions">
              <button class="btn btn-outline btn-action" title="收藏">
                <i class="ri-star-line"></i>
              </button>
              <button class="btn btn-outline btn-action" title="编辑">
                <i class="ri-edit-line"></i>
              </button>
              <button class="btn btn-primary btn-action" title="使用">
                <i class="ri-send-plane-line"></i> 使用
              </button>
            </div>
          </div>
        </div>
        
        <!-- 提示词卡片 6 -->
        <div class="card prompt-card">
          <div class="prompt-card-header">
            <div class="prompt-title">会议总结助手</div>
            <div class="prompt-category">工作效率</div>
          </div>
          <div class="prompt-card-body">
            <p class="prompt-description">将会议记录整理成结构化的摘要，突出重点和行动项。</p>
            <div class="prompt-content">你是一位专业的会议记录整理专家。请帮我将以下会议记录转化为一份结构清晰、重点突出的会议总结。总结应包含：
1. 会议基本信息（主题、时间、参与者）
2. 讨论的主要议题和决策要点
3. 明确的行动项和负责人
4. 需要跟进的未解决问题
5. 下一步计划和时间线

请使用简洁专业的语言，重点突出关键决策和行动项。</div>
          </div>
          <div class="prompt-card-footer">
            <div class="prompt-stats">
              <span><i class="ri-star-line"></i> 4.9</span>
              <span><i class="ri-time-line"></i> 2天前更新</span>
            </div>
            <div class="prompt-actions">
              <button class="btn btn-outline btn-action" title="收藏">
                <i class="ri-star-line"></i>
              </button>
              <button class="btn btn-outline btn-action" title="编辑">
                <i class="ri-edit-line"></i>
              </button>
              <button class="btn btn-primary btn-action" title="使用">
                <i class="ri-send-plane-line"></i> 使用
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- 新建提示词模态框 -->
  <div class="modal" id="prompt-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">创建新提示词</h3>
        <span class="modal-close">&times;</span>
      </div>
      <form class="modal-form">
        <div class="form-group">
          <label class="form-label">提示词名称</label>
          <input type="text" class="form-input" placeholder="给你的提示词起个名字" required>
        </div>
        <div class="form-group">
          <label class="form-label">分类</label>
          <select class="form-select" required>
            <option value="">请选择分类</option>
            <option value="writing">写作创作</option>
            <option value="programming">编程开发</option>
            <option value="data">数据分析</option>
            <option value="education">教育学习</option>
            <option value="life">生活助手</option>
            <option value="work">工作效率</option>
            <option value="other">其他</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label">描述</label>
          <input type="text" class="form-input" placeholder="简短描述此提示词的用途">
        </div>
        <div class="form-group">
          <label class="form-label">提示词内容</label>
          <textarea class="form-textarea" placeholder="输入你的提示词模板..." required></textarea>
        </div>
        <div class="form-group">
          <label class="form-label">提示词变量</label>
          <div style="display: flex; gap: 0.5rem; margin-bottom: 0.5rem;">
            <input type="text" class="form-input" placeholder="变量名称" style="flex: 1;">
            <input type="text" class="form-input" placeholder="默认值" style="flex: 1;">
            <button type="button" class="btn btn-icon" style="flex-shrink: 0;">
              <i class="ri-delete-bin-line"></i>
            </button>
          </div>
          <button type="button" class="btn btn-outline" style="width: 100%;">
            <i class="ri-add-line" style="margin-right: 0.4rem;"></i>添加变量
          </button>
        </div>
        <div class="form-group">
          <label class="form-label">可见性</label>
          <div style="display: flex; gap: 1rem;">
            <label style="display: flex; align-items: center; gap: 0.4rem; cursor: pointer;">
              <input type="radio" name="visibility" value="private" checked>
              <span>仅自己可见</span>
            </label>
            <label style="display: flex; align-items: center; gap: 0.4rem; cursor: pointer;">
              <input type="radio" name="visibility" value="public">
              <span>公开分享</span>
            </label>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-cancel">取消</button>
          <button type="submit" class="btn btn-primary">保存提示词</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    // 主题切换功能
    const themeToggle = document.getElementById('theme-toggle');
    const body = document.body;
    
    themeToggle.addEventListener('click', () => {
      body.classList.toggle('dark-mode');
      const icon = themeToggle.querySelector('i');
      
      if (body.classList.contains('dark-mode')) {
        icon.classList.remove('ri-moon-line');
        icon.classList.add('ri-sun-line');
      } else {
        icon.classList.remove('ri-sun-line');
        icon.classList.add('ri-moon-line');
      }
    });
    
    // 筛选标签切换
    const filterTags = document.querySelectorAll('.filter-tag');
    filterTags.forEach(tag => {
      tag.addEventListener('click', () => {
        filterTags.forEach(t => t.classList.remove('active'));
        tag.classList.add('active');
      });
    });
    
    // 分类标签切换
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        tabs.forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
      });
    });
    
    // 模态框功能
    const modal = document.getElementById('prompt-modal');
    const newPromptBtn = document.getElementById('new-prompt-btn');
    const closeBtn = document.querySelector('.modal-close');
    const cancelBtn = document.querySelector('.btn-cancel');
    
    newPromptBtn.addEventListener('click', () => {
      modal.style.display = 'block';
    });
    
    closeBtn.addEventListener('click', () => {
      modal.style.display = 'none';
    });
    
    cancelBtn.addEventListener('click', () => {
      modal.style.display = 'none';
    });
    
    window.addEventListener('click', (event) => {
      if (event.target === modal) {
        modal.style.display = 'none';
      }
    });
    
    // 收藏功能
    const starBtns = document.querySelectorAll('.prompt-actions .ri-star-line');
    starBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        if (btn.classList.contains('ri-star-line')) {
          btn.classList.remove('ri-star-line');
          btn.classList.add('ri-star-fill');
          btn.parentElement.setAttribute('title', '取消收藏');
        } else {
          btn.classList.remove('ri-star-fill');
          btn.classList.add('ri-star-line');
          btn.parentElement.setAttribute('title', '收藏');
        }
      });
    });
  </script>
</body>
</html> 